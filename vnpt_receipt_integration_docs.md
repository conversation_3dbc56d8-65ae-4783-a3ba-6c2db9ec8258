# Tài liệu tích hợp kỹ thuật - <PERSON><PERSON> thống biên lai điện tử VNPT

## <PERSON><PERSON><PERSON> lụ<PERSON>

1. [Tổng quan hệ thống](#tổng-quan-hệ-thống)
2. [Ph<PERSON>t hành biên lai](#phát-hành-biên-lai)
3. [Điều chỉnh biên lai](#điều-chỉnh-biên-lai)
4. [Thay thế biên lai](#thay-thế-biên-lai)
5. [Xem thông tin biên lai](#xem-thông-tin-biên-lai)
6. [Portal khách hàng](#portal-khách-hàng)
7. [Thay đổi theo <PERSON>hị định 70](#thay-đổi-theo-nghị-định-70)
8. [Cấu trúc XML](#cấu-trúc-xml)

---

## Tổng quan hệ thống

### Phương thức tích hợp
- **<PERSON><PERSON><PERSON>ứ<PERSON>**: Webservice
- **<PERSON><PERSON><PERSON> dạng URL**: `Domain_đơn_vị/Webservice`
- **Địa chỉ test**: `https://xxxxxxxxxx.vnpt-invoice.com.vn/`

### Xác thực hệ thống
Hệ thống sử dụng 2 loại tài khoản:
- **Account/ACPass**: Tài khoản nhân viên thực hiện các thao tác
- **Username/Password**: Tài khoản khách hàng để gọi service

---

## Phát hành biên lai

### Webservice: publishservice.asmx

#### Hàm: ImportAndPublishInv

```csharp
String ImportAndPublishInv(
    string Account,      // Tài khoản nhân viên
    string ACpass,       // Mật khẩu nhân viên
    string xmlInvData,   // Dữ liệu XML biên lai
    string username,     // Tài khoản khách hàng
    string password,     // Mật khẩu khách hàng
    string pattern,      // Pattern biên lai
    string serial,       // Serial biên lai
    int convert          // Convert TCVN3 (0: Không, 1: Có)
)
```

#### Giới hạn
- Tối đa **5000 biên lai** mỗi lần gọi

#### Kết quả trả về

| Mã lỗi | Mô tả | Ghi chú |
|--------|--------|---------|
| ERR:1  | Tài khoản đăng nhập sai hoặc không có quyền | |
| ERR:3  | Dữ liệu XML không đúng quy định | Chỉ cần 1 invoice không hợp lệ sẽ rollback tất cả |
| ERR:7  | Username không phù hợp | Không tìm thấy company tương ứng |
| ERR:20 | Pattern và serial không phù hợp | Chấp nhận cả 2 hoặc để trống cả 2 |
| ERR:5  | Không phát hành được biên lai | Database rollback |
| **OK** | **Thành công** | `OK:pattern;serial1-num11,num12;serial2-num21,num22` |

---

## Điều chỉnh biên lai

### Webservice: BusinessService.asmx

#### Hàm: adjustInv

```csharp
String adjustInv(
    string Account,      // Tài khoản nhân viên
    string ACpass,       // Mật khẩu nhân viên
    string xmlInvData,   // Dữ liệu XML biên lai cũ và mới
    string username,     // Tài khoản khách hàng
    string pass,         // Mật khẩu khách hàng
    string fkey,         // Key xác định biên lai cần điều chỉnh
    string AttachFile,   // Không sử dụng, để ""
    int convert          // Convert TCVN3
)
```

#### Loại điều chỉnh (Type)
- **2**: Điều chỉnh tăng
- **3**: Điều chỉnh giảm  
- **4**: Điều chỉnh thông tin

#### Kết quả trả về

| Mã lỗi | Mô tả |
|--------|--------|
| ERR:1  | Tài khoản đăng nhập sai |
| ERR:2  | Biên lai cần điều chỉnh không tồn tại |
| ERR:3  | Dữ liệu XML không đúng quy định |
| ERR:5  | Không phát hành được biên lai |
| ERR:6  | Dải biên lai cũ đã hết |
| ERR:8  | Biên lai đã bị thay thế, không thể điều chỉnh |
| ERR:9  | Trạng thái biên lai không được điều chỉnh |
| **OK** | **Thành công**: `OK:pattern;serial;invNumber` |

---

## Thay thế biên lai

### Webservice: BusinessService.asmx

#### Hàm: replaceInv

```csharp
String replaceInv(
    string Account,      // Tài khoản nhân viên
    string Acpass,       // Mật khẩu nhân viên
    string xmlInvData,   // Dữ liệu XML biên lai cũ và thay thế
    string username,     // Tài khoản khách hàng
    string pass,         // Mật khẩu khách hàng
    string fkey,         // Key xác định biên lai cần thay thế
    string Attachfile,   // Không sử dụng, để null
    string convert       // Convert TCVN3
)
```

#### Kết quả trả về

| Mã lỗi | Mô tả |
|--------|--------|
| ERR:2  | Không tồn tại biên lai cần thay thế |
| ERR:8  | Biên lai đã được thay thế rồi |
| ERR:9  | Trạng thái biên lai không được thay thế |
| **OK** | **Thành công**: `OK:pattern;serial;invNumber` |

---

## Xem thông tin biên lai

### Webservice: PortalService.asmx

#### 1. Lấy thông tin chi tiết biên lai

```csharp
// Cần thanh toán mới xem được
String getInvViewFkey(string fkey, string userName, string userPass)

// Không cần thanh toán
String getInvViewFkeyNoPay(string fkey, string userName, string userPass)
```

**Trả về**: Chuỗi HTML hiển thị biên lai

#### 2. Download biên lai PDF

```csharp
// Cần thanh toán
String downloadInvPDFFkey(string fkey, string userName, string userPass)

// Không cần thanh toán  
String downloadInvPDFFkeyNoPay(string fkey, string userName, string userPass)
```

**Trả về**: Chuỗi Base64 của file PDF

#### Kết quả lỗi chung

| Mã lỗi | Mô tả |
|--------|--------|
| ERR:1  | Tài khoản đăng nhập sai |
| ERR:6  | Chuỗi Fkey không chính xác |
| ERR:7  | Công ty không tồn tại |
| ERR:11 | Biên lai chưa được thanh toán |

---

## Portal khách hàng

### Webservice: PortalService.asmx

#### 1. Lấy danh sách biên lai theo mã khách hàng

```csharp
String listInvByCus(
    string cusCode,      // Mã công dân
    string fromDate,     // Ngày bắt đầu (dd/MM/yyyy)
    string toDate,       // Ngày kết thúc (dd/MM/yyyy)
    string userName,     // Tài khoản
    string userPass      // Mật khẩu
)
```

#### 2. Lấy danh sách biên lai theo fkey

```csharp
// So sánh bằng
String listInvByCusFkey(string key, string fromDate, string toDate, string userName, string userPass)

// So sánh chứa (contains)
String listInvByCusFkeyVNP(string key, string fromDate, string toDate, string userName, string userPass)
```

#### Cấu trúc XML trả về

```xml
<Data>
    <Item>
        <index>Tháng xuất biên lai</index>
        <invToken>Chuỗi token xác định biên lai</invToken>
        <fkey>Chuỗi định nghĩa biên lai</fkey>
        <name>Tên biên lai</name>
        <publishDate>Ngày phát hành biên lai</publishDate>
        <signStatus>Trạng thái ký khách hàng</signStatus>
        <pattern>Mẫu biên lai</pattern>
        <serial>Serial biên lai</serial>
        <invNum>Số biên lai</invNum>
        <payment>Trạng thái thanh toán (0,1)</payment>
        <Amount>Tổng tiền của biên lai</Amount>
        <status>Trạng thái biên lai (1,3,4)</status>
        <cusname>Tên khách hàng</cusname>
    </Item>
</Data>
```

#### Trạng thái biên lai

| Status | Mô tả |
|--------|--------|
| 1      | Biên lai đã phát hành |
| 3      | Biên lai bị thay thế |
| 4      | Biên lai bị điều chỉnh |

#### 3. Download biên lai gốc

```csharp
// Theo Fkey
String downloadInvFkey(string fkey, string userName, string userPass)          // Cần thanh toán
String downloadInvFkeyNoPay(string fkey, string userName, string userPass)     // Không cần thanh toán

// Theo Token  
String downloadInv(string invToken, string userName, string userPass)          // Cần thanh toán
String downloadInvNoPay(string invToken, string userName, string userPass)     // Không cần thanh toán
```

**Trả về**: Chuỗi XML biên lai gốc

#### 4. Chuyển đổi biên lai

```csharp
// Chuyển đổi để lưu trữ
String convertForStore(string invToken, string userName, string userPass)

// Chuyển đổi để chứng minh nguồn gốc
String convertForVerify(string invToken, string userName, string userPass)
```

---

## Thay đổi theo Nghị định 70

### Các thay đổi chính

1. **Bỏ chức năng hủy biên lai**
   - Không còn quy định hủy biên lai
   - Hệ thống đã chặn thao tác hủy với biên lai mới
   - Biên lai cũ vẫn có thể hủy được

2. **Chuyển sang thay thế/điều chỉnh**
   - Các nghiệp vụ cần chuyển sang sử dụng thay thế hoặc điều chỉnh

3. **Thêm trường dữ liệu mới**
   - **MDVQHNSach**: Mã quan hệ ngân sách
   - **CCCDan**: Căn cước công dân

---

## Cấu trúc XML

### 1. XML Phát hành biên lai (Publish.xml)

```xml
<Invoices>
    <Inv>
        <key>abcd</key> <!-- Key duy nhất -->
        <Invoice>
            <CusCode>KH000356</CusCode> <!-- Mã người nộp tiền/mã công dân -->
            <ArisingDate>22/08/2017</ArisingDate> <!-- Ngày tạo biên lai -->
            <CusName>Trần Văn Hảo</CusName> <!-- Tên người nộp tiền -->
            <Total>0</Total> <!-- Chỉ cần có thẻ -->
            <Amount>90000</Amount> <!-- Số tiền nộp -->
            <AmountInWords>Chín mươi ngàn đồng</AmountInWords> <!-- Số tiền bằng chữ -->
            <VATAmount>0</VATAmount> <!-- Bắt buộc có thẻ, mặc định = 0 -->
            <VATRate>0</VATRate> <!-- Bắt buộc có thẻ, mặc định = 0 -->
            <CusAddress></CusAddress> <!-- Chỉ cần có thẻ -->
            <PaymentMethod>TM</PaymentMethod> <!-- Hình thức thanh toán -->
            <Extra>Phí thành lập doanh nghiệp</Extra> <!-- Tên loại phí -->
            
            <Products> <!-- Bắt buộc có thẻ -->
                <Product> <!-- Chi tiết từng khoản thu -->
                    <Code></Code> <!-- Mã thủ tục hành chính -->
                    <ProdName></ProdName> <!-- Tên thủ tục hành chính -->
                    <ProdUnit>lần</ProdUnit> <!-- Đơn vị tính -->
                    <ProdQuantity></ProdQuantity> <!-- Số lượng -->
                    <ProdPrice></ProdPrice> <!-- Đơn giá -->
                    <Total></Total> <!-- Tổng tiền thu -->
                    <Amount></Amount> <!-- Tổng tiền thu -->
                    <VATRate>0</VATRate> <!-- Thuế GTGT -->
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>
```

### 2. XML Điều chỉnh biên lai (DieuChinh.xml)

```xml
<AdjustInv>
    <key>Chuỗi xác định biên lai mới (max 100 ký tự)</key>
    <CusCode>Mã khách hàng (max 50 ký tự)</CusCode>
    <CusName>Tên khách hàng (max 200 ký tự)</CusName>
    <CusAddress>Địa chỉ khách hàng (max 300 ký tự)</CusAddress>
    <CusPhone>Điện thoại khách hàng (max 50 ký tự)</CusPhone>
    <CusTaxCode>Mã số thuế KH (max 50 ký tự)</CusTaxCode>
    <PaymentMethod>Phương thức thanh toán (max 150 ký tự)</PaymentMethod>
    <KindOfService>Tháng biên lai (max 200 ký tự)</KindOfService>
    <Type>2</Type> <!-- 2: Tăng, 3: Giảm, 4: Thông tin -->
    
    <Products>
        <Product>
            <ProdName>Tên khoản thu (max 200 ký tự)</ProdName>
            <ProdUnit>Đơn vị tính (max 50 ký tự)</ProdUnit>
            <ProdQuantity>Số lượng (Decimal)</ProdQuantity>
            <ProdPrice>Đơn giá (Decimal)</ProdPrice>
            <Amount>Tổng tiền (Decimal)</Amount>
        </Product>
    </Products>
    
    <Total>Tổng tiền trước thuế (Decimal)</Total>
    <VATRate>Thuế GTGT (Float)</VATRate>
    <VATAmount>Tiền thuế GTGT (Decimal)</VATAmount>
    <Amount>Tổng tiền (Decimal)</Amount>
    <AmountInWords>Số tiền viết bằng chữ (max 255 ký tự)</AmountInWords>
    <Extra>Phí thành lập doanh nghiệp</Extra>
</AdjustInv>
```

### 3. XML Thay thế biên lai (Replace.xml)

```xml
<ReplaceInv>
    <!-- Cấu trúc tương tự AdjustInv nhưng không có thẻ Type -->
    <key>Chuỗi xác định biên lai mới (max 100 ký tự)</key>
    <CusCode>Mã khách hàng (max 50 ký tự)</CusCode>
    <CusName>Tên khách hàng (max 200 ký tự)</CusName>
    <CusAddress>Địa chỉ khách hàng (max 300 ký tự)</CusAddress>
    <CusPhone>Điện thoại khách hàng (max 50 ký tự)</CusPhone>
    <CusTaxCode>Mã số thuế KH (max 50 ký tự)</CusTaxCode>
    <PaymentMethod>Phương thức thanh toán (max 150 ký tự)</PaymentMethod>
    <KindOfService>Tháng biên lai (max 200 ký tự)</KindOfService>
    
    <Products>
        <Product>
            <ProdName>Tên sản phẩm (max 200 ký tự)</ProdName>
            <ProdUnit>Đơn vị tính (max 50 ký tự)</ProdUnit>
            <ProdQuantity>Số lượng (Decimal)</ProdQuantity>
            <ProdPrice>Đơn giá (Decimal)</ProdPrice>
            <Amount>Tổng tiền (Decimal)</Amount>
        </Product>
    </Products>
    
    <Total>Tổng tiền trước thuế (Decimal)</Total>
    <VATRate>Thuế GTGT (Float)</VATRate>
    <VATAmount>Tiền thuế GTGT (Decimal)</VATAmount>
    <Amount>Tổng tiền (Decimal)</Amount>
    <AmountInWords>Số tiền viết bằng chữ (max 255 ký tự)</AmountInWords>
    <Extra>Phí thành lập doanh nghiệp</Extra>
</ReplaceInv>
```

---

## Ghi chú quan trọng

### Quy tắc chung
- Tất cả trường có dấu `*` là **bắt buộc**
- **Convert**: 0 = Không convert TCVN3, 1 = Convert TCVN3 sang Unicode
- **Pattern và Serial**: Chấp nhận cả 2 hoặc để trống cả 2
- **Fkey**: Chuỗi định danh duy nhất cho mỗi biên lai

### Xử lý lỗi
- Tiền tố `ERR:` cho biết có lỗi xảy ra
- Tiền tố `OK:` cho biết thành công
- Một biên lai không hợp lệ sẽ làm rollback toàn bộ batch

### Bảo mật
- Sử dụng HTTPS cho tất cả kết nối
- Xác thực 2 lớp: Account nhân viên + Username khách hàng
- Kiểm tra quyền truy cập cho từng thao tác

---

*Tài liệu được tổng hợp từ các file kỹ thuật VNPT. Liên hệ: <EMAIL> để được hỗ trợ.*