@echo off
echo.
echo ========================================
echo    Build Tool Import Bien Lai VNPT
echo    Tao file EXE tu Python source
echo    (c) 2025 Trung tam CNTT - VNPT Ca Mau
echo ========================================
echo.

REM Kiem tra Python
echo [INFO] Kiem tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Loi: Python chua duoc cai dat!
    echo [INFO] Vui long cai dat Python 3.8+ tu https://python.org
    echo.
    pause
    exit /b 1
) else (
    echo [OK] Python da duoc cai dat
)

echo.
echo [INFO] Kiem tra PyInstaller...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo [INFO] Cai dat PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo [ERROR] Loi: Khong the cai dat PyInstaller!
        echo.
        pause
        exit /b 1
    )
    echo [OK] PyInstaller da duoc cai dat
) else (
    echo [OK] PyInstaller da san sang
)

echo.
echo [INFO] Don dep build cu...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo [OK] Da don dep

echo.
echo [INFO] Bat dau build EXE...
echo [INFO] Qua trinh nay co the mat vai phut...
echo.

pyinstaller --clean build_exe.spec

if errorlevel 1 (
    echo.
    echo [ERROR] Build that bai!
    echo [INFO] Kiem tra loi o tren de biet chi tiet
    echo.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build thanh cong!
echo [INFO] File EXE duoc tao tai: dist\VNPT_Invoice_Import_Tool.exe
echo [INFO] Kich thuoc file:

if exist "dist\VNPT_Invoice_Import_Tool.exe" (
    for %%I in ("dist\VNPT_Invoice_Import_Tool.exe") do echo    %%~zI bytes
    echo.
    echo [INFO] Huong dan su dung:
    echo    1. Copy file EXE den may can su dung
    echo    2. Khong can cai dat Python hay dependencies
    echo    3. Double-click de chay tool
    echo.
    echo [QUESTION] Ban co muon chay thu file EXE khong? (Y/N)
    set /p choice=
    if /i "!choice!"=="Y" (
        echo [INFO] Dang khoi chay...
        start "" "dist\VNPT_Invoice_Import_Tool.exe"
    )
) else (
    echo [ERROR] Khong tim thay file EXE!
)

echo.
pause
