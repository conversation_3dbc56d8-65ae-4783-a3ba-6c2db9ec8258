2025-08-13 07:51:22,927 - INFO - <PERSON><PERSON><PERSON><PERSON> thể kết nối đến VNPT
2025-08-13 07:58:29,331 - INFO - 💾 <PERSON><PERSON> lưu cấu hình
2025-08-13 07:58:31,207 - INFO - ✅ Kết nối VNPT thành công
2025-08-13 07:58:31,383 - INFO - 📊 <PERSON><PERSON><PERSON> qu<PERSON> test endpoints: {'PublishService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx'}, 'BusinessService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/BusinessService.asmx'}, 'PortalService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PortalService.asmx'}}
2025-08-13 07:58:42,558 - INFO - 📝 Đã tạo file Excel mẫu: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 07:58:46,732 - INFO - Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 07:59:00,471 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 07:59:12,852 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 07:59:12,858 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 07:59:12,885 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 07:59:12,890 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 07:59:15,340 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:07:00,489 - INFO - ✅ Kết nối VNPT thành công
2025-08-13 08:07:00,594 - INFO - 📊 Kết quả test endpoints: {'PublishService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx'}, 'BusinessService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/BusinessService.asmx'}, 'PortalService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PortalService.asmx'}}
2025-08-13 08:07:06,043 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 08:07:06,234 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:07:12,816 - ERROR - VNPT HTTP Error: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:12,823 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:12,855 - ERROR - VNPT HTTP Error: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:12,861 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:14,881 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:07:25,390 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:07:28,759 - ERROR - VNPT HTTP Error: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:28,766 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:28,792 - ERROR - VNPT HTTP Error: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:28,798 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 415 Client Error: Unsupported Media Type for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:07:29,689 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:09:37,926 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/test_data.xlsx
2025-08-13 08:09:38,109 - INFO - Đã load 5 dòng dữ liệu từ Excel
2025-08-13 08:09:42,419 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 08:09:42,430 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:09:46,692 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:09:46,697 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:09:46,722 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:09:46,728 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:09:48,139 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:10:11,582 - INFO - ❌ Không thể kết nối đến VNPT
2025-08-13 08:10:16,425 - INFO - ✅ Kết nối VNPT thành công
2025-08-13 08:10:16,601 - INFO - 📊 Kết quả test endpoints: {'PublishService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx'}, 'BusinessService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/BusinessService.asmx'}, 'PortalService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PortalService.asmx'}}
2025-08-13 08:10:20,029 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:10:20,030 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:10:20,058 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:10:20,059 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:10:21,349 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:16:28,566 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:16:28,570 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:16:28,602 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:16:28,605 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:16:30,145 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:19:02,357 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:19:02,533 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "GET / HTTP/1.1" 302 145
2025-08-13 08:19:02,563 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "GET /Account/LogOn?ReturnUrl=%2F HTTP/1.1" 200 3858
2025-08-13 08:19:03,492 - INFO - ✅ Kết nối VNPT thành công
2025-08-13 08:19:03,516 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "GET /PublishService.asmx HTTP/1.1" 200 2206
2025-08-13 08:19:03,543 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "GET /BusinessService.asmx HTTP/1.1" 200 2354
2025-08-13 08:19:03,571 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "GET /PortalService.asmx HTTP/1.1" 200 2407
2025-08-13 08:19:03,593 - INFO - 📊 Kết quả test endpoints: {'PublishService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx'}, 'BusinessService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/BusinessService.asmx'}, 'PortalService.asmx': {'status': 'accessible', 'status_code': 200, 'url': 'https://2001382676adminadmindemo.vnpt-invoice.com.vn/PortalService.asmx'}}
2025-08-13 08:19:12,011 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 08:19:12,197 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:19:14,887 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:14,887 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 08:19:14,888 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>cap1service</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813081914</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <CusName><![CDATA[Nguyễn Văn A]]></CusName>
            <CusAddress><![CDATA[123 Đường ABC, Quận 1, TP.HCM]]></CusAddress>
            <CusTaxCode>*********</CusTaxCode>
            <PaymentMethod></PaymentMethod>
            <KindOfService></KindOfService>
            <Products>
                <Product>
                    <ProdName><![CDATA[Tiền điện tháng 1/2024]]></ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Amount>1000000</Amount>
                    <VATRate>10</VATRate>
                    <VATAmount>100000</VATAmount>
                    <Total>1100000</Total>
                </Product>
            </Products>
            <Total>1100000</Total>
            <DiscountAmount>0</DiscountAmount>
            <VATAmount>100000</VATAmount>
            <Amount>1000000</Amount>
            <AmountInWords><![CDATA[1.100.000 đồng]]></AmountInWords>
            <IssuedDate>2024-01-15</IssuedDate>
            <Extra><![CDATA[nan]]></Extra>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>2001382676adminadmin</username>
      <password>Cmu#2025</password>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
      <convert>0</convert>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 08:19:14,891 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:19:14,979 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 400 0
2025-08-13 08:19:14,981 - DEBUG - Response Status: 400
2025-08-13 08:19:14,985 - DEBUG - Response Headers: {'Cache-Control': 'private', 'Content-Type': 'text/xml; charset=utf-8', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 01:18:30 GMT', 'Content-Length': '0'}
2025-08-13 08:19:14,987 - DEBUG - Response Body: 
2025-08-13 08:19:14,989 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:14,998 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:15,006 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:15,007 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 08:19:15,008 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>cap1service</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813081915</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <CusName><![CDATA[Trần Thị B]]></CusName>
            <CusAddress><![CDATA[456 Đường XYZ, Quận 2, TP.HCM]]></CusAddress>
            <CusTaxCode>*********</CusTaxCode>
            <PaymentMethod></PaymentMethod>
            <KindOfService></KindOfService>
            <Products>
                <Product>
                    <ProdName><![CDATA[Tiền điện tháng 1/2024]]></ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Amount>2000000</Amount>
                    <VATRate>10</VATRate>
                    <VATAmount>200000</VATAmount>
                    <Total>2200000</Total>
                </Product>
            </Products>
            <Total>2200000</Total>
            <DiscountAmount>0</DiscountAmount>
            <VATAmount>200000</VATAmount>
            <Amount>2000000</Amount>
            <AmountInWords><![CDATA[2.200.000 đồng]]></AmountInWords>
            <IssuedDate>2024-01-15</IssuedDate>
            <Extra><![CDATA[Khách hàng VIP]]></Extra>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>2001382676adminadmin</username>
      <password>Cmu#2025</password>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
      <convert>0</convert>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 08:19:15,032 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 400 0
2025-08-13 08:19:15,034 - DEBUG - Response Status: 400
2025-08-13 08:19:15,035 - DEBUG - Response Headers: {'Cache-Control': 'private', 'Content-Type': 'text/xml; charset=utf-8', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 01:18:30 GMT', 'Content-Length': '0'}
2025-08-13 08:19:15,037 - DEBUG - Response Body: 
2025-08-13 08:19:15,037 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:15,043 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:19:16,377 - INFO - 🎉 Import hoàn thành: 0/2 thành công
2025-08-13 08:27:34,035 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 08:27:34,203 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:27:38,958 - INFO - 🚀 Bắt đầu Import & Publish 2 biên lai...
2025-08-13 08:27:38,961 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:38,963 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:27:39,141 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 400 0
2025-08-13 08:27:39,142 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:39,147 - INFO - Lỗi dòng 1: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:39,149 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:39,171 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 400 0
2025-08-13 08:27:39,172 - ERROR - VNPT HTTP Error: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:39,177 - INFO - Lỗi dòng 2: Lỗi kết nối VNPT: 400 Client Error: Bad Request for url: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:27:40,769 - INFO - 🎉 Import & Publish hoàn thành: 0/2 thành công
2025-08-13 08:31:36,676 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 08:31:36,861 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 08:31:40,277 - INFO - 🚀 Bắt đầu Import & Publish 2 biên lai...
2025-08-13 08:31:40,280 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:31:40,282 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:31:40,478 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 317
2025-08-13 08:31:40,478 - INFO - Published invoices: {'success': False, 'error': 'ERR:1', 'message': 'Error publishing invoices'}
2025-08-13 08:31:40,481 - INFO - Lỗi dòng 1: Error publishing invoices
2025-08-13 08:31:40,484 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:31:40,591 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 317
2025-08-13 08:31:40,592 - INFO - Published invoices: {'success': False, 'error': 'ERR:1', 'message': 'Error publishing invoices'}
2025-08-13 08:31:40,594 - INFO - Lỗi dòng 2: Error publishing invoices
2025-08-13 08:31:42,079 - INFO - 🎉 Import & Publish hoàn thành: 0/2 thành công
2025-08-13 08:32:19,322 - INFO - 💾 Đã lưu cấu hình
2025-08-13 08:32:25,322 - INFO - 🚀 Bắt đầu Import & Publish 2 biên lai...
2025-08-13 08:32:25,333 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:32:25,334 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:32:25,959 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 357
2025-08-13 08:32:25,959 - INFO - Published invoices: {'success': True, 'message': 'Invoices published successfully', 'data': 'OK:01BLP0-001;CA-25E-KH001_20250813083225_5'}
2025-08-13 08:32:25,962 - INFO - Thành công dòng 1: Nguyễn Văn A
2025-08-13 08:32:25,964 - DEBUG - Calling VNPT API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:32:26,275 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 358
2025-08-13 08:32:26,278 - INFO - Published invoices: {'success': True, 'message': 'Invoices published successfully', 'data': 'OK:01BLP0-001;CA-25E-KH002_20250813083225_6'}
2025-08-13 08:32:26,281 - INFO - Thành công dòng 2: Trần Thị B
2025-08-13 08:32:28,839 - INFO - 🎉 Import & Publish hoàn thành: 2/2 thành công
2025-08-13 08:32:46,757 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 08:32:46,771 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:32:46,774 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 08:32:46,775 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813083246</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Nguyễn Văn A</CusName>
            <Total>1100000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.100.000 đồng</AmountInWords>
            <VATAmount>100000</VATAmount>
            <VATRate>10</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>10</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 08:32:46,776 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 08:32:47,037 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 345
2025-08-13 08:32:47,040 - DEBUG - Response Status: 200
2025-08-13 08:32:47,040 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 01:32:01 GMT', 'Content-Length': '345'}
2025-08-13 08:32:47,041 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:01BLP0-001;CA-25E-KH001_20250813083246</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 08:32:47,044 - INFO - Thành công dòng 1: Nguyễn Văn A
2025-08-13 08:32:47,047 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 08:32:47,050 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 08:32:47,052 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813083247</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Trần Thị B</CusName>
            <Total>2200000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.200.000 đồng</AmountInWords>
            <VATAmount>200000</VATAmount>
            <VATRate>10</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>10</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 08:32:47,232 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 346
2025-08-13 08:32:47,234 - DEBUG - Response Status: 200
2025-08-13 08:32:47,234 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 01:32:02 GMT', 'Content-Length': '346'}
2025-08-13 08:32:47,235 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:01BLP0-001;CA-25E-KH002_20250813083247</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 08:32:47,237 - INFO - Thành công dòng 2: Trần Thị B
2025-08-13 08:32:49,166 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 11:18:14,044 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau.xlsx
2025-08-13 11:18:14,320 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 11:50:50,834 - INFO - 📝 Đã tạo file Excel mẫu: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 11:50:50,851 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 11:53:06,220 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 11:53:06,390 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:01:49,159 - INFO - 📝 Đã tạo file Excel mẫu: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 12:01:49,176 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:02:01,425 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:02:01,428 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:02:01,428 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:02:01,428 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813120201</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Nguyễn Văn A</BuyerName>
                <BuyerLegalName>Nguyễn Văn A</BuyerLegalName>
                <BuyerDisplayName>Nguyễn Văn A</BuyerDisplayName>
                <BuyerAddressLine>123 Đường ABC, Quận 1, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB001</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:02:01,433 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 12:02:01,740 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:02:01,741 - DEBUG - Response Status: 200
2025-08-13 12:02:01,741 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:01:17 GMT', 'Content-Length': '308'}
2025-08-13 12:02:01,741 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:02:01,743 - INFO - Thành công dòng 1: N/A
2025-08-13 12:02:01,744 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:02:01,745 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:02:01,745 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813120201</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Trần Thị B</BuyerName>
                <BuyerLegalName>Trần Thị B</BuyerLegalName>
                <BuyerDisplayName>Trần Thị B</BuyerDisplayName>
                <BuyerAddressLine>456 Đường XYZ, Quận 2, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB002</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:02:01,858 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:02:01,859 - DEBUG - Response Status: 200
2025-08-13 12:02:01,859 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:01:17 GMT', 'Content-Length': '308'}
2025-08-13 12:02:01,859 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:02:01,861 - INFO - Thành công dòng 2: N/A
2025-08-13 12:02:04,024 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 12:07:43,446 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:07:43,450 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:07:43,450 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:07:43,450 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813120743</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Nguyễn Văn A</BuyerName>
                <BuyerLegalName>Nguyễn Văn A</BuyerLegalName>
                <BuyerDisplayName>Nguyễn Văn A</BuyerDisplayName>
                <BuyerAddressLine>123 Đường ABC, Quận 1, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB001</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:07:43,452 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 12:07:45,728 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:07:45,728 - DEBUG - Response Status: 200
2025-08-13 12:07:45,728 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:07:01 GMT', 'Content-Length': '308'}
2025-08-13 12:07:45,729 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:07:45,729 - INFO - Thành công dòng 1: N/A
2025-08-13 12:07:45,732 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:07:45,732 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:07:45,732 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813120745</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Trần Thị B</BuyerName>
                <BuyerLegalName>Trần Thị B</BuyerLegalName>
                <BuyerDisplayName>Trần Thị B</BuyerDisplayName>
                <BuyerAddressLine>456 Đường XYZ, Quận 2, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB002</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:07:45,841 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:07:45,842 - DEBUG - Response Status: 200
2025-08-13 12:07:45,842 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:07:01 GMT', 'Content-Length': '308'}
2025-08-13 12:07:45,842 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:07:45,843 - INFO - Thành công dòng 2: N/A
2025-08-13 12:07:47,642 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 12:09:01,785 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:09:01,788 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:09:01,788 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:09:01,788 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813120901</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Nguyễn Văn A</BuyerName>
                <BuyerLegalName>Nguyễn Văn A</BuyerLegalName>
                <BuyerDisplayName>Nguyễn Văn A</BuyerDisplayName>
                <BuyerAddressLine>123 Đường ABC, Quận 1, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB001</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:09:01,789 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 12:09:01,986 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:09:01,987 - DEBUG - Response Status: 200
2025-08-13 12:09:01,987 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:08:17 GMT', 'Content-Length': '308'}
2025-08-13 12:09:01,987 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:09:01,988 - INFO - Thành công dòng 1: N/A
2025-08-13 12:09:01,991 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:09:01,991 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:09:01,991 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813120901</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <Buyer>
                <BuyerName>Trần Thị B</BuyerName>
                <BuyerLegalName>Trần Thị B</BuyerLegalName>
                <BuyerDisplayName>Trần Thị B</BuyerDisplayName>
                <BuyerAddressLine>456 Đường XYZ, Quận 2, TP.HCM</BuyerAddressLine>
                <BuyerPostalCode></BuyerPostalCode>
                <BuyerDistrictName></BuyerDistrictName>
                <BuyerCityName></BuyerCityName>
                <BuyerCountryCode>VN</BuyerCountryCode>
                <BuyerPhoneNumber></BuyerPhoneNumber>
                <BuyerFaxNumber></BuyerFaxNumber>
                <BuyerEmail></BuyerEmail>
                <BuyerBankName></BuyerBankName>
                <BuyerBankAccount></BuyerBankAccount>
                <BuyerTaxCode>*********</BuyerTaxCode>
                <MDVQHNSach>MB002</MDVQHNSach>
                <CCCDan>*********0</CCCDan>
            </Buyer>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:09:02,110 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 308
2025-08-13 12:09:02,110 - DEBUG - Response Status: 200
2025-08-13 12:09:02,110 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:08:17 GMT', 'Content-Length': '308'}
2025-08-13 12:09:02,111 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:09:02,111 - INFO - Thành công dòng 2: N/A
2025-08-13 12:09:03,072 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 12:11:58,470 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 12:11:58,657 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:12:01,235 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:12:01,238 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:12:01,238 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:12:01,238 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813121201</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Buyer>Nguyễn Văn A</Buyer>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <MDVQHNSach>MB001</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:12:01,241 - DEBUG - Starting new HTTPS connection (1): 2001382676adminadmindemo.vnpt-invoice.com.vn:443
2025-08-13 12:12:01,622 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 344
2025-08-13 12:12:01,623 - DEBUG - Response Status: 200
2025-08-13 12:12:01,623 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:11:17 GMT', 'Content-Length': '344'}
2025-08-13 12:12:01,623 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:01BLP0-001;CA-25E-KH001_20250813121201</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:12:01,625 - INFO - Thành công dòng 1: N/A
2025-08-13 12:12:01,627 - DEBUG - Calling VNPT Import API: https://2001382676adminadmindemo.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:12:01,628 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:12:01,628 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>2001382676adminadmin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813121201</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Buyer>Trần Thị B</Buyer>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <MDVQHNSach>MB002</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cap1service</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>2001382676adminadmin</tkTao>
      <pattern>01BLP0-001</pattern>
      <serial>CA-25E</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:12:01,808 - DEBUG - https://2001382676adminadmindemo.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 346
2025-08-13 12:12:01,809 - DEBUG - Response Status: 200
2025-08-13 12:12:01,809 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0\r\n X-XSS-Protection: 1', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:11:17 GMT', 'Content-Length': '346'}
2025-08-13 12:12:01,809 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:01BLP0-001;CA-25E-KH002_20250813121201</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:12:01,811 - INFO - Thành công dòng 2: N/A
2025-08-13 12:12:04,030 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 12:13:18,371 - INFO - 💾 Đã lưu cấu hình
2025-08-13 12:13:38,026 - INFO - 💾 Đã lưu cấu hình
2025-08-13 12:13:44,372 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:13:44,385 - DEBUG - Calling VNPT Import API: https://**********-012admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:13:44,388 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:13:44,388 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********-012admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813121344</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Buyer>Nguyễn Văn A</Buyer>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <MDVQHNSach>MB001</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cnvinhloiservice</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********-012admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:13:44,390 - DEBUG - Starting new HTTPS connection (1): **********-012admin.vnpt-invoice.com.vn:443
2025-08-13 12:13:45,531 - DEBUG - https://**********-012admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 341
2025-08-13 12:13:45,532 - DEBUG - Response Status: 200
2025-08-13 12:13:45,532 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:13:45 GMT', 'Content-Length': '341'}
2025-08-13 12:13:45,532 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH001_20250813121344</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:13:45,535 - INFO - Thành công dòng 1: N/A
2025-08-13 12:13:45,540 - DEBUG - Calling VNPT Import API: https://**********-012admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:13:45,541 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:13:45,542 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********-012admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813121345</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Buyer>Trần Thị B</Buyer>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <MDVQHNSach>MB002</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cnvinhloiservice</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********-012admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:13:46,560 - DEBUG - https://**********-012admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 342
2025-08-13 12:13:46,561 - DEBUG - Response Status: 200
2025-08-13 12:13:46,563 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:13:46 GMT', 'Content-Length': '342'}
2025-08-13 12:13:46,564 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH002_20250813121345</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:13:46,568 - INFO - Thành công dòng 2: N/A
2025-08-13 12:13:47,976 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 12:20:33,440 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 12:20:33,620 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:22:25,611 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/test_import_modes_20250813_082746.xlsx
2025-08-13 12:22:25,745 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:54:48,709 - INFO - 📝 Đã tạo file Excel mẫu: D:/BienLaiDienTu/tool/file-mau-moi-1.xlsx
2025-08-13 12:54:48,726 - INFO - Đã load 0 dòng dữ liệu từ Excel
2025-08-13 12:54:55,902 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/tool/file-mau-moi.xlsx
2025-08-13 12:54:55,909 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 12:55:01,770 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 12:55:01,788 - DEBUG - Calling VNPT Import API: https://**********-012admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:55:01,788 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:55:01,788 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********-012admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH001_20250813125501</key>
        <Invoice>
            <CusCode>KH001</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH ABC</CusName>
            <Buyer>Nguyễn Văn A</Buyer>
            <Total>1000000</Total>
            <Amount>1000000</Amount>
            <AmountInWords>1.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Đường ABC, Quận 1, TP.HCM</CusAddress>
            <MDVQHNSach>MB001</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>nan</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>1000000</ProdPrice>
                    <Total>1000000</Total>
                    <Amount>1000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cnvinhloiservice</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********-012admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:55:01,792 - DEBUG - Starting new HTTPS connection (1): **********-012admin.vnpt-invoice.com.vn:443
2025-08-13 12:55:03,052 - DEBUG - https://**********-012admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 341
2025-08-13 12:55:03,053 - DEBUG - Response Status: 200
2025-08-13 12:55:03,053 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:55:02 GMT', 'Content-Length': '341'}
2025-08-13 12:55:03,053 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH001_20250813125501</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:55:03,055 - INFO - Thành công dòng 1: N/A
2025-08-13 12:55:03,057 - DEBUG - Calling VNPT Import API: https://**********-012admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 12:55:03,057 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 12:55:03,057 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********-012admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH002_20250813125503</key>
        <Invoice>
            <CusCode>KH002</CusCode>
            <ArisingDate>15/01/2024</ArisingDate>
            <CusName>Công ty TNHH XYZ</CusName>
            <Buyer>Trần Thị B</Buyer>
            <Total>2000000</Total>
            <Amount>2000000</Amount>
            <AmountInWords>2.000.000 đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Đường XYZ, Quận 2, TP.HCM</CusAddress>
            <MDVQHNSach>MB002</MDVQHNSach>
            <CCCDan>*********0</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Khách hàng VIP</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Tiền điện tháng 1/2024</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>2000000</ProdPrice>
                    <Total>2000000</Total>
                    <Amount>2000000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>cnvinhloiservice</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********-012admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 12:55:04,080 - DEBUG - https://**********-012admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/1.1" 200 342
2025-08-13 12:55:04,081 - DEBUG - Response Status: 200
2025-08-13 12:55:04,081 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 05:55:03 GMT', 'Content-Length': '342'}
2025-08-13 12:55:04,081 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH002_20250813125503</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 12:55:04,083 - INFO - Thành công dòng 2: N/A
2025-08-13 12:55:05,106 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:15:54,469 - INFO - 💾 Đã lưu cấu hình
2025-08-13 16:16:10,749 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:16:10,954 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:16:22,644 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:16:22,653 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:16:22,653 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:16:22,653 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813161622</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:16:22,657 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:16:23,527 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:16:23,528 - DEBUG - Response Status: 200
2025-08-13 16:16:23,530 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:16:22 GMT', 'Content-Length': '308'}
2025-08-13 16:16:23,530 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:16:23,543 - INFO - Thành công dòng 1: N/A
2025-08-13 16:16:23,598 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:16:23,601 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:16:23,601 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813161623</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:16:24,469 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:16:24,471 - DEBUG - Response Status: 200
2025-08-13 16:16:24,474 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:16:23 GMT', 'Content-Length': '308'}
2025-08-13 16:16:24,476 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:16:24,479 - INFO - Thành công dòng 2: N/A
2025-08-13 16:16:25,721 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:18:29,746 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Copy of Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:18:29,754 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:18:35,158 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:18:35,160 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:18:35,160 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:18:35,160 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813161835</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:18:35,161 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:18:35,876 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:18:35,876 - DEBUG - Response Status: 200
2025-08-13 16:18:35,876 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:18:35 GMT', 'Content-Length': '308'}
2025-08-13 16:18:35,876 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:18:35,878 - INFO - Thành công dòng 1: N/A
2025-08-13 16:18:35,880 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:18:35,880 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:18:35,881 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813161835</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:18:36,534 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:18:36,534 - DEBUG - Response Status: 200
2025-08-13 16:18:36,535 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:18:35 GMT', 'Content-Length': '308'}
2025-08-13 16:18:36,535 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:18:36,536 - INFO - Thành công dòng 2: N/A
2025-08-13 16:18:37,651 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:20:02,924 - INFO - 💾 Đã lưu cấu hình
2025-08-13 16:22:24,362 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:22:34,367 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:22:34,370 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:22:34,370 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:22:34,371 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162234</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:22:34,372 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:22:35,250 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:22:35,250 - DEBUG - Response Status: 200
2025-08-13 16:22:35,251 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:22:34 GMT', 'Content-Length': '308'}
2025-08-13 16:22:35,251 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:22:35,253 - INFO - Thành công dòng 1: N/A
2025-08-13 16:22:35,255 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:22:35,255 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:22:35,255 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162235</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:22:35,843 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:22:35,844 - DEBUG - Response Status: 200
2025-08-13 16:22:35,844 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:22:34 GMT', 'Content-Length': '308'}
2025-08-13 16:22:35,844 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:22:35,846 - INFO - Thành công dòng 2: N/A
2025-08-13 16:22:37,729 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:24:37,288 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:24:40,163 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:24:40,182 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:24:40,182 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:24:40,183 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162440</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:24:40,185 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:24:41,248 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:24:41,248 - DEBUG - Response Status: 200
2025-08-13 16:24:41,248 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:24:40 GMT', 'Content-Length': '308'}
2025-08-13 16:24:41,248 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:24:41,251 - INFO - Thành công dòng 1: N/A
2025-08-13 16:24:41,254 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:24:41,254 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:24:41,254 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162441</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:24:42,019 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:24:42,020 - DEBUG - Response Status: 200
2025-08-13 16:24:42,020 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:24:41 GMT', 'Content-Length': '308'}
2025-08-13 16:24:42,020 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:24:42,022 - INFO - Thành công dòng 2: N/A
2025-08-13 16:24:45,637 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:29:43,210 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:29:45,716 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:29:45,719 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:29:45,719 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:29:45,719 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162945</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:29:45,721 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:29:46,482 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:29:46,483 - DEBUG - Response Status: 200
2025-08-13 16:29:46,483 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:29:45 GMT', 'Content-Length': '308'}
2025-08-13 16:29:46,483 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:29:46,485 - INFO - Thành công dòng 1: N/A
2025-08-13 16:29:46,488 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:29:46,488 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:29:46,488 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813162946</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:29:47,034 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:29:47,035 - DEBUG - Response Status: 200
2025-08-13 16:29:47,036 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:29:46 GMT', 'Content-Length': '308'}
2025-08-13 16:29:47,036 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:29:47,038 - INFO - Thành công dòng 2: N/A
2025-08-13 16:29:56,084 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:32:16,012 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/demo_bien_lai_20250813_075218.xlsx
2025-08-13 16:32:16,020 - INFO - Đã load 10 dòng dữ liệu từ Excel
2025-08-13 16:32:20,590 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/demo_bien_lai_20250813_075730.xlsx
2025-08-13 16:32:20,597 - INFO - Đã load 10 dòng dữ liệu từ Excel
2025-08-13 16:32:23,668 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/demo_bien_lai_20250813_075218.xlsx
2025-08-13 16:32:23,675 - INFO - Đã load 10 dòng dữ liệu từ Excel
2025-08-13 16:32:27,168 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/test_import_modes_20250813_082746.xlsx
2025-08-13 16:32:27,174 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:32:41,633 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:32:41,635 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:32:41,635 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:32:41,635 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>TEST001_20250813163241</key>
        <Invoice>
            <CusCode>TEST001</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer></Buyer>
            <Total>55000</Total>
            <Amount>50000</Amount>
            <AmountInWords>Năm mươi năm nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>123 Test Street</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Test mode 1</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Test import only</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>50000</ProdPrice>
                    <Total>50000</Total>
                    <Amount>50000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:32:41,636 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:32:43,129 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 342
2025-08-13 16:32:43,130 - DEBUG - Response Status: 200
2025-08-13 16:32:43,130 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:32:41 GMT', 'Content-Length': '342'}
2025-08-13 16:32:43,130 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-TEST001_20250813163241</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:32:43,133 - INFO - Thành công dòng 1: Khách hàng Test 1
2025-08-13 16:32:43,136 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:32:43,140 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:32:43,141 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>TEST002_20250813163243</key>
        <Invoice>
            <CusCode>TEST002</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer></Buyer>
            <Total>82500</Total>
            <Amount>75000</Amount>
            <AmountInWords>Tám mươi hai nghìn năm trăm đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>456 Demo Avenue</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>Test mode 2</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Test import & publish</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>75000</ProdPrice>
                    <Total>75000</Total>
                    <Amount>75000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:32:43,929 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:32:43,930 - DEBUG - Response Status: 200
2025-08-13 16:32:43,930 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:32:42 GMT', 'Content-Length': '308'}
2025-08-13 16:32:43,930 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:32:43,933 - INFO - Thành công dòng 2: Khách hàng Test 2
2025-08-13 16:33:03,851 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:33:32,418 - INFO - 📂 Đã chọn file: D:/BienLaiDienTu/toolv2/test_import_modes_20250813_082746.xlsx
2025-08-13 16:33:32,424 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:33:44,168 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:33:44,175 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:33:52,012 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Copy of Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:33:52,019 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:33:57,288 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:33:57,300 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:33:57,301 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:33:57,302 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163357</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:33:57,305 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:33:58,168 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:33:58,168 - DEBUG - Response Status: 200
2025-08-13 16:33:58,168 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:33:57 GMT', 'Content-Length': '308'}
2025-08-13 16:33:58,168 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:33:58,170 - INFO - Thành công dòng 1: N/A
2025-08-13 16:33:58,172 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:33:58,172 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:33:58,173 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163358</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>Lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:33:59,177 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:33:59,178 - DEBUG - Response Status: 200
2025-08-13 16:33:59,178 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:33:58 GMT', 'Content-Length': '308'}
2025-08-13 16:33:59,178 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:33:59,180 - INFO - Thành công dòng 2: N/A
2025-08-13 16:34:00,242 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:35:12,500 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:35:15,309 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:35:15,313 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:35:15,314 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:35:15,314 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163515</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:35:15,316 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:35:16,298 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:35:16,298 - DEBUG - Response Status: 200
2025-08-13 16:35:16,298 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:35:15 GMT', 'Content-Length': '308'}
2025-08-13 16:35:16,298 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:35:16,300 - INFO - Thành công dòng 1: N/A
2025-08-13 16:35:16,302 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:35:16,302 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:35:16,303 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163516</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:35:17,118 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:35:17,118 - DEBUG - Response Status: 200
2025-08-13 16:35:17,118 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:35:16 GMT', 'Content-Length': '308'}
2025-08-13 16:35:17,118 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:35:17,121 - INFO - Thành công dòng 2: N/A
2025-08-13 16:35:19,940 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:37:50,820 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:37:53,199 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:37:53,200 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:37:53,200 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:37:53,200 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163753</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:37:53,203 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:37:54,262 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:37:54,263 - DEBUG - Response Status: 200
2025-08-13 16:37:54,263 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:37:53 GMT', 'Content-Length': '308'}
2025-08-13 16:37:54,263 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:37:54,265 - INFO - Thành công dòng 1: N/A
2025-08-13 16:37:54,267 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:37:54,267 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:37:54,267 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163754</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:37:55,035 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:37:55,035 - DEBUG - Response Status: 200
2025-08-13 16:37:55,035 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:37:53 GMT', 'Content-Length': '308'}
2025-08-13 16:37:55,035 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:37:55,037 - INFO - Thành công dòng 2: N/A
2025-08-13 16:37:58,080 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:38:42,939 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:38:45,267 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:38:45,280 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:38:45,280 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:38:45,280 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163845</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>20000</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:38:45,284 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:38:46,250 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:38:46,252 - DEBUG - Response Status: 200
2025-08-13 16:38:46,252 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:38:45 GMT', 'Content-Length': '308'}
2025-08-13 16:38:46,252 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:38:46,254 - INFO - Thành công dòng 1: N/A
2025-08-13 16:38:46,256 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:38:46,256 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:38:46,256 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813163846</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>70000</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:38:46,940 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:38:46,941 - DEBUG - Response Status: 200
2025-08-13 16:38:46,941 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:38:46 GMT', 'Content-Length': '308'}
2025-08-13 16:38:46,941 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:38:46,945 - INFO - Thành công dòng 2: N/A
2025-08-13 16:38:49,555 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:40:32,225 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:40:34,787 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:40:34,799 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:40:34,799 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:40:34,799 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813164034</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress></CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:40:34,802 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:40:35,665 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:40:35,665 - DEBUG - Response Status: 200
2025-08-13 16:40:35,666 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:40:34 GMT', 'Content-Length': '308'}
2025-08-13 16:40:35,666 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:40:35,668 - INFO - Thành công dòng 1: N/A
2025-08-13 16:40:35,671 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:40:35,671 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:40:35,671 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813164035</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress></CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:40:36,245 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:40:36,246 - DEBUG - Response Status: 200
2025-08-13 16:40:36,246 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:40:35 GMT', 'Content-Length': '308'}
2025-08-13 16:40:36,246 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:40:36,249 - INFO - Thành công dòng 2: N/A
2025-08-13 16:40:39,464 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:41:28,695 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:41:31,573 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:41:31,574 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:41:31,575 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:41:31,575 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813164131</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:41:31,578 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:41:32,328 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:41:32,328 - DEBUG - Response Status: 200
2025-08-13 16:41:32,329 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:41:31 GMT', 'Content-Length': '308'}
2025-08-13 16:41:32,329 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:41:32,331 - INFO - Thành công dòng 1: N/A
2025-08-13 16:41:32,333 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:41:32,333 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:41:32,333 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>_20250813164132</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:41:32,988 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:41:32,989 - DEBUG - Response Status: 200
2025-08-13 16:41:32,989 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:41:31 GMT', 'Content-Length': '308'}
2025-08-13 16:41:32,989 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:41:32,992 - INFO - Thành công dòng 2: N/A
2025-08-13 16:41:35,960 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:49:15,013 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:49:20,206 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:49:20,214 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:49:20,214 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:49:20,214 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813164920</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:49:20,216 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:49:21,621 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 343
2025-08-13 16:49:21,622 - DEBUG - Response Status: 200
2025-08-13 16:49:21,622 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:49:21 GMT', 'Content-Length': '343'}
2025-08-13 16:49:21,622 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813164920</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:49:21,623 - INFO - Thành công dòng 1: N/A
2025-08-13 16:49:21,625 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:49:21,626 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:49:21,626 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813164921</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>kWh</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:49:22,809 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 343
2025-08-13 16:49:22,809 - DEBUG - Response Status: 200
2025-08-13 16:49:22,809 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:49:22 GMT', 'Content-Length': '343'}
2025-08-13 16:49:22,809 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813164921</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:49:22,812 - INFO - Thành công dòng 2: N/A
2025-08-13 16:49:23,883 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:54:49,691 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:54:49,864 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:54:52,874 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:54:52,885 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:54:52,885 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:54:52,885 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813165452</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:54:52,888 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:54:53,666 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:54:53,666 - DEBUG - Response Status: 200
2025-08-13 16:54:53,666 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:54:52 GMT', 'Content-Length': '308'}
2025-08-13 16:54:53,667 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:54:53,669 - INFO - Thành công dòng 1: N/A
2025-08-13 16:54:53,671 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:54:53,671 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:54:53,672 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813165453</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:54:54,257 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:54:54,258 - DEBUG - Response Status: 200
2025-08-13 16:54:54,258 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:54:52 GMT', 'Content-Length': '308'}
2025-08-13 16:54:54,258 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:54:54,261 - INFO - Thành công dòng 2: N/A
2025-08-13 16:54:55,955 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:56:50,282 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:56:53,020 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:56:53,035 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:56:53,035 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:56:53,035 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813165653021001</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:56:53,038 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:56:54,643 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 348
2025-08-13 16:56:54,644 - DEBUG - Response Status: 200
2025-08-13 16:56:54,644 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:56:53 GMT', 'Content-Length': '348'}
2025-08-13 16:56:54,644 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813165653021001</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:56:54,646 - INFO - Thành công dòng 1: N/A
2025-08-13 16:56:54,648 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:56:54,648 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:56:54,648 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813165654646002</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:56:55,882 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 350
2025-08-13 16:56:55,882 - DEBUG - Response Status: 200
2025-08-13 16:56:55,883 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:56:55 GMT', 'Content-Length': '350'}
2025-08-13 16:56:55,883 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813165654646002</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:56:55,885 - INFO - Thành công dòng 2: N/A
2025-08-13 16:57:01,812 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 16:57:24,732 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 16:57:24,739 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 16:57:28,118 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 16:57:28,121 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:57:28,121 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:57:28,122 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813165728119003</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:57:28,123 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 16:57:28,795 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:57:28,795 - DEBUG - Response Status: 200
2025-08-13 16:57:28,796 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:57:27 GMT', 'Content-Length': '308'}
2025-08-13 16:57:28,796 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:57:28,798 - INFO - Thành công dòng 1: N/A
2025-08-13 16:57:28,801 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 16:57:28,802 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 16:57:28,803 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813165728799004</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 16:57:29,387 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 16:57:29,388 - DEBUG - Response Status: 200
2025-08-13 16:57:29,388 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 09:57:28 GMT', 'Content-Length': '308'}
2025-08-13 16:57:29,389 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 16:57:29,391 - INFO - Thành công dòng 2: N/A
2025-08-13 16:57:30,697 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:00:51,845 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:00:52,027 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:01:07,266 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:01:07,279 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:01:07,279 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:01:07,279 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813170107266001</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:01:07,282 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:01:08,045 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:01:08,045 - DEBUG - Response Status: 200
2025-08-13 17:01:08,045 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:01:07 GMT', 'Content-Length': '308'}
2025-08-13 17:01:08,045 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:01:08,048 - INFO - Thành công dòng 1: N/A
2025-08-13 17:01:08,051 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:01:08,051 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:01:08,051 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>VNPT_20250813170108049002</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:01:08,814 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:01:08,814 - DEBUG - Response Status: 200
2025-08-13 17:01:08,815 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:01:07 GMT', 'Content-Length': '308'}
2025-08-13 17:01:08,815 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:01:08,819 - INFO - Thành công dòng 2: N/A
2025-08-13 17:02:18,758 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:02:24,169 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Copy of Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:02:24,176 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:02:31,482 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:02:31,498 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:02:31,498 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:02:31,498 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813170231482003</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:02:31,499 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:02:32,771 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 348
2025-08-13 17:02:32,771 - DEBUG - Response Status: 200
2025-08-13 17:02:32,771 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:02:31 GMT', 'Content-Length': '348'}
2025-08-13 17:02:32,771 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813170231482003</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:02:32,773 - INFO - Thành công dòng 1: N/A
2025-08-13 17:02:32,776 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:02:32,777 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:02:32,777 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_20250813170232774004</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:02:33,887 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 349
2025-08-13 17:02:33,887 - DEBUG - Response Status: 200
2025-08-13 17:02:33,888 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:02:32 GMT', 'Content-Length': '349'}
2025-08-13 17:02:33,888 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_20250813170232774004</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:02:33,890 - INFO - Thành công dòng 2: N/A
2025-08-13 17:02:34,939 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:07:57,721 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:07:57,896 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:08:01,200 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:08:01,211 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:08:01,211 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:08:01,211 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>20250813170801200001</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:08:01,214 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:08:02,142 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:08:02,142 - DEBUG - Response Status: 200
2025-08-13 17:08:02,142 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:08:01 GMT', 'Content-Length': '308'}
2025-08-13 17:08:02,142 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:08:02,144 - INFO - Thành công dòng 1: N/A
2025-08-13 17:08:02,147 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:08:02,147 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:08:02,147 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>20250813170802145002</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:08:02,884 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:08:02,885 - DEBUG - Response Status: 200
2025-08-13 17:08:02,885 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:08:01 GMT', 'Content-Length': '308'}
2025-08-13 17:08:02,885 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:08:02,887 - INFO - Thành công dòng 2: N/A
2025-08-13 17:08:04,132 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:11:14,660 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:11:14,836 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:11:17,558 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:11:17,561 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:11:17,561 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:11:17,561 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>cbba98345bfb427eba13a0de57930697</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:11:17,564 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:11:18,316 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:11:18,316 - DEBUG - Response Status: 200
2025-08-13 17:11:18,316 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:11:17 GMT', 'Content-Length': '308'}
2025-08-13 17:11:18,316 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:11:18,320 - INFO - Thành công dòng 1: N/A
2025-08-13 17:11:18,322 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:11:18,322 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:11:18,322 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>3d8426aa022f458c9df95c60e689a261</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:11:18,884 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:11:18,885 - DEBUG - Response Status: 200
2025-08-13 17:11:18,885 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:11:18 GMT', 'Content-Length': '308'}
2025-08-13 17:11:18,885 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:11:18,887 - INFO - Thành công dòng 2: N/A
2025-08-13 17:11:21,238 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:12:01,079 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:12:01,093 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:12:01,093 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:12:01,093 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>afba842cffc8480792568efdf7bc82d5</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:12:01,094 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:12:01,836 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:12:01,837 - DEBUG - Response Status: 200
2025-08-13 17:12:01,837 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:12:00 GMT', 'Content-Length': '308'}
2025-08-13 17:12:01,837 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:12:01,839 - INFO - Thành công dòng 1: N/A
2025-08-13 17:12:01,842 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:12:01,842 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:12:01,842 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>f0cfd5abc1e745aca3fc3838d263a5ba</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:12:02,464 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:12:02,464 - DEBUG - Response Status: 200
2025-08-13 17:12:02,465 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:12:01 GMT', 'Content-Length': '308'}
2025-08-13 17:12:02,465 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:12:02,467 - INFO - Thành công dòng 2: N/A
2025-08-13 17:12:05,096 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:12:35,983 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Copy of Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:12:35,991 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:14:04,643 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:14:04,807 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:14:07,675 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:14:07,686 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:14:07,686 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:14:07,686 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_001_46789fb6b14048648bc1b5f19a040ee8</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:14:07,689 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:14:08,490 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:14:08,491 - DEBUG - Response Status: 200
2025-08-13 17:14:08,491 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:14:07 GMT', 'Content-Length': '308'}
2025-08-13 17:14:08,491 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:14:08,496 - INFO - Thành công dòng 1: N/A
2025-08-13 17:14:08,522 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:14:08,524 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:14:08,525 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_001_11f9f2061cc8424ea913ea68d936d59e</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:14:09,105 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:14:09,105 - DEBUG - Response Status: 200
2025-08-13 17:14:09,105 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:14:08 GMT', 'Content-Length': '308'}
2025-08-13 17:14:09,106 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:14:09,109 - INFO - Thành công dòng 2: N/A
2025-08-13 17:14:12,894 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:14:28,668 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Copy of Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:14:28,675 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:14:32,039 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:14:32,059 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:14:32,060 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:14:32,060 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_f20ee119704747548edd1b0416f33981</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:14:32,061 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:14:33,278 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 360
2025-08-13 17:14:33,278 - DEBUG - Response Status: 200
2025-08-13 17:14:33,278 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:14:32 GMT', 'Content-Length': '360'}
2025-08-13 17:14:33,278 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_f20ee119704747548edd1b0416f33981</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:14:33,281 - INFO - Thành công dòng 1: N/A
2025-08-13 17:14:33,283 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:14:33,283 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:14:33,283 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_ccd4baff066c47708b35f100e690050a</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach>MH1</MDVQHNSach>
            <CCCDan>***********</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:14:34,575 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 360
2025-08-13 17:14:34,576 - DEBUG - Response Status: 200
2025-08-13 17:14:34,576 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:14:33 GMT', 'Content-Length': '360'}
2025-08-13 17:14:34,576 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_ccd4baff066c47708b35f100e690050a</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:14:34,580 - INFO - Thành công dòng 2: N/A
2025-08-13 17:14:39,237 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:16:01,314 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:16:01,491 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:16:05,057 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:16:05,058 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:16:05,058 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:16:05,058 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_98c6488376a24ce6a583ec4116a44a77</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:16:05,061 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:16:05,763 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:16:05,763 - DEBUG - Response Status: 200
2025-08-13 17:16:05,763 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:16:04 GMT', 'Content-Length': '308'}
2025-08-13 17:16:05,763 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:16:05,766 - INFO - Thành công dòng 1: N/A
2025-08-13 17:16:05,768 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:16:05,768 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:16:05,768 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_27599eaf66ab415db0d4fe921e0ef9b3</key>
        <Invoice>
            <CusCode></CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:16:06,388 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 308
2025-08-13 17:16:06,389 - DEBUG - Response Status: 200
2025-08-13 17:16:06,389 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:16:05 GMT', 'Content-Length': '308'}
2025-08-13 17:16:06,389 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>ERR:3</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:16:06,391 - INFO - Thành công dòng 2: N/A
2025-08-13 17:16:14,574 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
2025-08-13 17:18:52,737 - INFO - 📂 Đã chọn file: C:/Users/<USER>/Downloads/Mẫu import Bien Lai BLU.xlsx
2025-08-13 17:18:52,931 - INFO - Đã load 2 dòng dữ liệu từ Excel
2025-08-13 17:18:57,238 - INFO - 🚀 Bắt đầu Import only 2 biên lai...
2025-08-13 17:18:57,251 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:18:57,251 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:18:57,251 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_cda7bed3f81144828fe43599d6d43868</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>20000</Total>
            <Amount>20000</Amount>
            <AmountInWords>Hai mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>20000</ProdPrice>
                    <Total>20000</Total>
                    <Amount>20000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:18:57,254 - DEBUG - Starting new HTTPS connection (1): **********008admin.vnpt-invoice.com.vn:443
2025-08-13 17:18:58,594 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 360
2025-08-13 17:18:58,595 - DEBUG - Response Status: 200
2025-08-13 17:18:58,595 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:18:57 GMT', 'Content-Length': '360'}
2025-08-13 17:18:58,595 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_cda7bed3f81144828fe43599d6d43868</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:18:58,597 - INFO - Thành công dòng 1: N/A
2025-08-13 17:18:58,600 - DEBUG - Calling VNPT Import API: https://**********008admin.vnpt-invoice.com.vn/PublishService.asmx
2025-08-13 17:18:58,600 - DEBUG - SOAP Request Headers: {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '"http://tempuri.org/ImportInv"'}
2025-08-13 17:18:58,600 - DEBUG - SOAP Request Body: <?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>**********008admin</Account>
      <ACpass>Cmu#2025</ACpass>
      <xmlInvData><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>KH_01_ef64eea52f7d45389fd4af4136d1009b</key>
        <Invoice>
            <CusCode>KH_01</CusCode>
            <ArisingDate>13/08/2025</ArisingDate>
            <CusName></CusName>
            <Buyer>Huỳnh Hán Sến</Buyer>
            <Total>70000</Total>
            <Amount>70000</Amount>
            <AmountInWords>Bảy mươi nghìn đồng</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>khóm 15, phường Bạc Liêu, tỉnh Cà Mau</CusAddress>
            <MDVQHNSach></MDVQHNSach>
            <CCCDan></CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra></Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>Phí đăng ký giao dịch bảo đảm</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>70000</ProdPrice>
                    <Total>70000</Total>
                    <Amount>70000</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>]]></xmlInvData>
      <username>vpdkbaclieu</username>
      <password>Cmu#2025</password>
      <convert>0</convert>
      <tkTao>**********008admin</tkTao>
      <pattern>EBL01-001</pattern>
      <serial>25T</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>
2025-08-13 17:18:59,752 - DEBUG - https://**********008admin.vnpt-invoice.com.vn:443 "POST /PublishService.asmx HTTP/11" 200 360
2025-08-13 17:18:59,753 - DEBUG - Response Status: 200
2025-08-13 17:18:59,753 - DEBUG - Response Headers: {'Cache-Control': 'private, max-age=0,no-store', 'Pragma': 'no-cache', 'Content-Type': 'text/xml; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'X-Frame-Options': 'DENY', 'X-Content-Type-Options': 'nosniff', 'Date': 'Wed, 13 Aug 2025 10:18:58 GMT', 'Content-Length': '360'}
2025-08-13 17:18:59,753 - DEBUG - Response Body: <?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><ImportInvResponse xmlns="http://tempuri.org/"><ImportInvResult>OK:EBL01-001;25T-KH_01_ef64eea52f7d45389fd4af4136d1009b</ImportInvResult></ImportInvResponse></soap:Body></soap:Envelope>
2025-08-13 17:18:59,755 - INFO - Thành công dòng 2: N/A
2025-08-13 17:19:01,552 - INFO - 🎉 Import only hoàn thành: 2/2 thành công
