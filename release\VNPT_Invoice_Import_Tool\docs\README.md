# ⚡ Tool Import Biên Lai Điện Tử - VNPT

Tool GUI hiện đại sử dụng Python với **ttkbootstrap** (Bootstrap-style) để import biên lai điện tử qua API VNPT.

**Bản quyền thuộc về Trung tâm CNTT - VNPT Cà Mau**
© 2025 VNPT Ca Mau IT Center. All rights reserved.

## ✨ Tính năng

- 🎨 **Giao diện Bootstrap đẹp mắt** với ttkbootstrap
- ⚙️ **Cấu hình dễ dàng** và lưu trữ thông tin kết nối VNPT
- 📝 **Tạo file Excel mẫu** tự động
- 📊 **Import dữ liệu từ Excel** với auto-load và preview
- 👁️ **Xem trước dữ liệu** tự động sau khi chọn file
- 🚀 **Import biên lai qua API VNPT** với 2 chế độ:
  - 📥 **Import Only**: Chỉ import, không publish
  - 🚀 **Import & Publish**: Import và publish cùng lúc
- 📋 **Log chi tiết** quá trình import
- 📈 **Progress bar** hiển thị tiến độ
- 🎯 **Status indicator** hiển thị trạng thái kết nối
- 🌈 **Theme Bootstrap** với nhiều màu sắc

## Cài đặt

1. **Cài đặt Python 3.8+**

2. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

3. **Chạy ứng dụng:**
```bash
python invoice_import_tool.py
```

## Hướng dẫn sử dụng

### 1. Cấu hình VNPT

1. Mở tab **"Cấu hình"**
2. Nhập thông tin kết nối VNPT:
   - URL VNPT
   - Account (tài khoản nhân viên)
   - AC Password (mật khẩu nhân viên)
   - Username (tài khoản khách hàng)
   - Password (mật khẩu khách hàng)
3. Cấu hình bổ sung (tùy chọn):
   - Pattern: Mẫu số hóa đơn
   - Serial: Ký hiệu hóa đơn
   - Convert: Chuyển đổi TCVN3 (0: Không, 1: Có)
   - Tự động publish: Tự động xuất biên lai sau khi import
4. Nhấn **"Test Kết Nối"** để kiểm tra
5. Nhấn **"Lưu Cấu Hình"** để lưu

### 2. Chuẩn bị dữ liệu Excel

1. Mở tab **"Import Excel"**
2. Nhấn **"Tạo Mẫu"** để tạo file Excel mẫu
3. Hoặc chuẩn bị file Excel với các cột:
   - `STT`: Số thứ tự
   - `Ma_KH`: Mã khách hàng
   - `DonViNop`: Đơn vị nộp (tên công ty/tổ chức)
   - `Dia_Chi`: Địa chỉ khách hàng
   - `MST`: Mã số thuế
   - `MDVQHNSach`: Mã quan hệ ngân sách
   - `CCCDan`: Căn cước công dân
   - `NguoiNopPhi`: Người nộp phí (tên cá nhân)
   - `So_Tien`: Số tiền
   - `Tong_Tien`: Tổng tiền (bằng So_Tien)
   - `Noi_Dung`: Nội dung biên lai
   - `Ngay_Lap`: Ngày lập (YYYY-MM-DD)
   - `Ghi_Chu`: Ghi chú

### 3. Import biên lai

1. Nhấn **"Chọn File"** để chọn file Excel
2. Nhấn **"Load Excel"** để xem trước dữ liệu
3. Kiểm tra dữ liệu trong bảng preview
4. Nhấn **"Import Biên Lai"** để bắt đầu import
5. Theo dõi tiến độ qua progress bar
6. Xem kết quả trong tab **"Log"**

## Cấu trúc file

```
├── invoice_import_tool.py    # Tool GUI chính
├── webservice_client.py     # Client API VNPT
├── requirements.txt         # Dependencies
├── README.md               # Hướng dẫn
├── config.json             # File cấu hình (tự động tạo)
└── invoice_import.log      # File log (tự động tạo)
```

## Lưu ý

- File cấu hình `config.json` sẽ được tạo tự động sau lần đầu lưu
- Log được ghi vào file `invoice_import.log` và hiển thị trong tab Log
- Mật khẩu được lưu trong file config (cần bảo mật file này)
- Tool hỗ trợ import hàng loạt với progress bar
- Có thể chọn import only hoặc import + publish

## Xử lý lỗi

- Kiểm tra kết nối mạng nếu không thể kết nối VNPT
- Kiểm tra thông tin đăng nhập nếu gặp lỗi xác thực
- Kiểm tra format dữ liệu Excel nếu gặp lỗi đọc file
- Xem chi tiết lỗi trong tab Log

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra file log `invoice_import.log`
2. Kiểm tra tab Log trong ứng dụng
3. Đảm bảo đã cài đặt đúng dependencies
4. Kiểm tra kết nối mạng và thông tin VNPT

## Bản quyền và Giấy phép

**© 2025 Trung tâm CNTT - VNPT Cà Mau**

### Thông tin bản quyền
- **Tổ chức**: Trung tâm Công nghệ Thông tin - VNPT Cà Mau
- **Năm phát hành**: 2024
- **Phát triển bởi**: Đội ngũ kỹ thuật VNPT Cà Mau
- **Liên hệ**: <EMAIL>
- **Website**: https://vnpt-camau.vn

### Điều khoản sử dụng
- Mọi quyền được bảo lưu (All rights reserved)
- Không được sao chép, phân phối hoặc sử dụng mà không có sự cho phép bằng văn bản
- Tool được phát triển để phục vụ nội bộ VNPT và đối tác được ủy quyền
- Việc sử dụng tool này đồng nghĩa với việc chấp nhận các điều khoản bản quyền

### Hỗ trợ kỹ thuật
- **Email**: <EMAIL>
- **Hotline**: 1900-xxxx (giờ hành chính)
- **Địa chỉ**: Trung tâm CNTT - VNPT Cà Mau

---
*Tool Import Biên Lai Điện Tử VNPT - Phiên bản 2.0 Bootstrap Edition*
