#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test để kiểm tra format key mới cho VNPT
"""

import pandas as pd
from datetime import datetime
import re

# Tạo class test với logic key mới
class TestVNPTKeyFormat:
    def __init__(self):
        self._invoice_counter = 0
    
    def create_invoice_xml(self, row) -> str:
        """Tạo XML data cho biên lai từ dữ liệu row theo format VNPT"""
        xml_template = """<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>{key}</key>
        <Invoice>
            <CusCode>{cus_code}</CusCode>
            <Total>{total}</Total>
        </Invoice>
    </Inv>
</Invoices>"""

        # Helper function
        def safe_get(value):
            return str(value).strip() if value and str(value).strip() != 'nan' else ''

        # Extract data từ row
        cus_code = safe_get(row.get('Ma_KH', ''))
        
        # Tạo key unique - xử lý trường hợp cus_code rỗng
        # Sử dụng timestamp với microseconds để đảm bảo tính duy nhất
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        microseconds = now.strftime('%f')[:3]  # Lấy 3 chữ số đầu của microseconds
        
        # Thêm counter để đảm bảo tính duy nhất tuyệt đối
        if not hasattr(self, '_invoice_counter'):
            self._invoice_counter = 0
        self._invoice_counter += 1
        
        unique_suffix = f"{timestamp}{microseconds}{self._invoice_counter:03d}"
        
        if cus_code and cus_code.strip():
            key = f"{cus_code.strip()}_{unique_suffix}"
        else:
            # Nếu không có mã khách hàng, chỉ dùng unique_suffix (không có prefix)
            # Vì hệ thống VNPT có thể không chấp nhận prefix VNPT_
            key = unique_suffix

        total = int(row.get('Tong_Tien', 100000))

        return xml_template.format(
            key=key,
            cus_code=cus_code,
            total=total
        )

def test_vnpt_key_format():
    """Test format key mới cho VNPT"""
    
    print("🧪 Test format key mới cho VNPT")
    print("=" * 50)
    
    # Tạo dữ liệu test với cả 2 trường hợp: có và không có mã KH
    test_data = [
        # Có mã khách hàng
        {'STT': 1, 'Ma_KH': 'KH_01', 'Tong_Tien': 100000},
        {'STT': 2, 'Ma_KH': 'KH002', 'Tong_Tien': 200000},
        {'STT': 3, 'Ma_KH': 'CUST123', 'Tong_Tien': 300000},
        
        # Không có mã khách hàng
        {'STT': 4, 'Ma_KH': '', 'Tong_Tien': 400000},
        {'STT': 5, 'Ma_KH': None, 'Tong_Tien': 500000},
        {'STT': 6, 'Ma_KH': '   ', 'Tong_Tien': 600000},  # Chỉ có khoảng trắng
    ]
    
    # Tạo DataFrame
    df = pd.DataFrame(test_data)
    
    # Tạo instance của tool test
    tool = TestVNPTKeyFormat()
    
    # Tạo XML cho từng hóa đơn và thu thập keys
    keys = []
    print("📋 Tạo key cho từng trường hợp:")
    print("-" * 40)
    
    for index, row in df.iterrows():
        xml_data = tool.create_invoice_xml(row)
        
        # Extract key từ XML
        key_match = re.search(r'<key>(.*?)</key>', xml_data)
        if key_match:
            key = key_match.group(1)
            keys.append(key)
            
            ma_kh = row.get('Ma_KH', '')
            ma_kh_display = f"'{ma_kh}'" if ma_kh and str(ma_kh).strip() else "RỖNG"
            print(f"Hóa đơn {index+1}: Mã KH = {ma_kh_display:10} → Key = {key}")
        else:
            print(f"Hóa đơn {index+1}: ❌ Không tìm thấy key")
    
    print("\n" + "=" * 50)
    print("📊 Phân tích kết quả:")
    
    # Phân loại key
    with_customer_code = []
    without_customer_code = []
    
    for i, key in enumerate(keys):
        if '_' in key:
            with_customer_code.append((i+1, key))
        else:
            without_customer_code.append((i+1, key))
    
    print(f"\n✅ Key có mã khách hàng ({len(with_customer_code)}):")
    for invoice_num, key in with_customer_code:
        print(f"  Hóa đơn {invoice_num}: {key}")
    
    print(f"\n🔢 Key không có mã khách hàng ({len(without_customer_code)}):")
    for invoice_num, key in without_customer_code:
        print(f"  Hóa đơn {invoice_num}: {key}")
    
    # Kiểm tra tính duy nhất
    print(f"\n🔍 Kiểm tra tính duy nhất:")
    print(f"  - Tổng số key: {len(keys)}")
    print(f"  - Số key duy nhất: {len(set(keys))}")
    
    if len(keys) == len(set(keys)):
        print("  ✅ Tất cả key đều duy nhất!")
    else:
        print("  ❌ Có key bị trùng lặp!")
    
    print(f"\n📝 Nhận xét:")
    print(f"  - Key có mã KH: Giữ format cũ với dấu gạch dưới")
    print(f"  - Key không có mã KH: Chỉ dùng timestamp+microseconds+counter")
    print(f"  - Loại bỏ prefix 'VNPT_' có thể gây lỗi với hệ thống VNPT")

if __name__ == "__main__":
    test_vnpt_key_format()
