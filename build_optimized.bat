@echo off
echo.
echo ========================================
echo    Build Optimized - VNPT Invoice Tool
echo    Toi uu dung luong va icon moi
echo    (c) 2025 Trung tam CNTT - VNPT Ca Mau  
echo ========================================
echo.

REM Kiem tra Python
echo [INFO] Kiem tra Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Loi: Python chua duoc cai dat!
    pause
    exit /b 1
) else (
    echo [OK] Python da duoc cai dat
)

REM Kiem tra icon
echo [INFO] Kiem tra icon moi...
if exist "icon.ico" (
    echo [OK] Tim thay icon.ico
    for %%I in ("icon.ico") do echo [INFO] Kich thuoc icon: %%~zI bytes
) else (
    echo [WARNING] Khong tim thay icon.ico, se su dung icon mac dinh
)

echo.
echo [INFO] Kiem tra UPX (toi uu nen)...
upx --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] UPX khong duoc cai dat
    echo [INFO] De toi uu toi da, tai UPX tu: https://upx.github.io/
    echo [INFO] Tiep tuc build ma khong UPX...
) else (
    echo [OK] UPX da san sang
)

echo.
echo [INFO] Don dep build cu...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo [OK] Da don dep

echo.
echo [INFO] Bat dau build toi uu...
echo [INFO] Su dung cac toi uu hoa:
echo        - Loai bo cac module khong can thiet
echo        - Strip symbols
echo        - Nen UPX (neu co)
echo        - Icon moi duoc cap nhat
echo.

REM Build với tối ưu hóa
pyinstaller --clean --log-level=WARN build_exe.spec

if errorlevel 1 (
    echo.
    echo [ERROR] Build that bai!
    echo [INFO] Kiem tra loi o tren de biet chi tiet
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build thanh cong!

if exist "dist\VNPT_Invoice_Import_Tool.exe" (
    echo [INFO] Thong tin file EXE:
    for %%I in ("dist\VNPT_Invoice_Import_Tool.exe") do (
        echo        Kich thuoc: %%~zI bytes
        set /a size_mb=%%~zI/1024/1024
        echo        Tuong duong: !size_mb! MB
    )
    
    echo.
    echo [INFO] So sanh voi phien ban truoc:
    echo        Phien ban truoc: ~104 MB
    echo        Phien ban moi: !size_mb! MB
    set /a saved=104-!size_mb!
    if !saved! GTR 0 (
        echo        Tiet kiem: !saved! MB
    )
    
    echo.
    echo [QUESTION] Ban co muon test file EXE khong? (Y/N)
    set /p choice=
    if /i "!choice!"=="Y" (
        echo [INFO] Dang khoi chay...
        start "" "dist\VNPT_Invoice_Import_Tool.exe"
    )
) else (
    echo [ERROR] Khong tim thay file EXE!
)

echo.
echo [INFO] Build hoan thanh!
pause
