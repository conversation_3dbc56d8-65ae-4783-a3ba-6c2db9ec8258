#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tạo icon đơn giản cho <PERSON>ng dụng
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_icon():
        """Tạo icon đơn giản cho ứng dụng"""
        
        # Tạo image 256x256
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Vẽ background gradient (xanh VNPT)
        for y in range(size):
            alpha = int(255 * (1 - y / size * 0.3))
            color = (0, 120, 215, alpha)  # Màu xanh VNPT
            draw.line([(0, y), (size, y)], fill=color)
        
        # Vẽ viền
        draw.rectangle([10, 10, size-10, size-10], outline=(255, 255, 255, 200), width=4)
        
        # Vẽ biểu tượng lightning (⚡)
        try:
            font_size = 120
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Vẽ text ⚡
        text = "⚡"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 20
        
        # Shadow
        draw.text((x+3, y+3), text, font=font, fill=(0, 0, 0, 100))
        # Main text
        draw.text((x, y), text, font=font, fill=(255, 255, 255, 255))
        
        # Vẽ text VNPT
        try:
            small_font = ImageFont.truetype("arial.ttf", 24)
        except:
            try:
                small_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 24)
            except:
                small_font = ImageFont.load_default()
        
        vnpt_text = "VNPT"
        bbox = draw.textbbox((0, 0), vnpt_text, font=small_font)
        text_width = bbox[2] - bbox[0]
        x = (size - text_width) // 2
        y = size - 60
        
        draw.text((x+1, y+1), vnpt_text, font=small_font, fill=(0, 0, 0, 150))
        draw.text((x, y), vnpt_text, font=small_font, fill=(255, 255, 255, 255))
        
        # Lưu các kích thước khác nhau
        sizes = [16, 32, 48, 64, 128, 256]
        images = []
        
        for s in sizes:
            resized = img.resize((s, s), Image.Resampling.LANCZOS)
            images.append(resized)
        
        # Lưu thành file ICO
        img.save('icon.ico', format='ICO', sizes=[(s, s) for s in sizes])
        print("✅ Đã tạo icon.ico thành công!")
        
        return True
        
    if __name__ == "__main__":
        create_icon()
        
except ImportError:
    print("⚠️ Không có PIL/Pillow, sẽ sử dụng icon mặc định")
    print("💡 Để tạo icon custom, cài đặt: pip install Pillow")
except Exception as e:
    print(f"⚠️ Lỗi tạo icon: {e}")
    print("💡 Sẽ sử dụng icon mặc định của PyInstaller")
