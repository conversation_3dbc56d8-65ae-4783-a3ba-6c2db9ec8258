#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script tạo dữ liệu demo cho tool import biên lai
"""

import pandas as pd
from datetime import datetime, timedelta
import random

def create_demo_excel():
    """Tạo file Excel demo với dữ liệu mẫu"""
    
    # Dữ liệu mẫu
    customers = [
        ("KH001", "Nguyễn Văn <PERSON>", "123 Đường <PERSON>, Quận 1, TP.HCM", "0123456789"),
        ("KH002", "<PERSON>r<PERSON><PERSON>", "456 Đ<PERSON>ờng <PERSON>, Quận 1, TP.HCM", "0987654321"),
        ("KH003", "<PERSON>ê Vă<PERSON>", "789 Đường <PERSON> T<PERSON>ng, <PERSON>uậ<PERSON> 3, TP.HC<PERSON>", "0111222333"),
        ("KH004", "<PERSON><PERSON><PERSON><PERSON>", "321 Đườ<PERSON> 8, <PERSON><PERSON><PERSON><PERSON> 10, TP.HCM", "0444555666"),
        ("KH005", "<PERSON><PERSON>ng Văn Em", "654 Đường <PERSON>, <PERSON>u<PERSON>n 3, <PERSON><PERSON>.<PERSON>M", "0777888999"),
        ("KH006", "Ng<PERSON> Th<PERSON> <PERSON><PERSON>ơng", "987 <PERSON><PERSON><PERSON>ng <PERSON><PERSON><PERSON>n <PERSON><PERSON><PERSON>n <PERSON><PERSON>, <PERSON>u<PERSON>n <PERSON><PERSON>nh <PERSON>h<PERSON>nh, <PERSON><PERSON>.<PERSON><PERSON>", "0333444555"),
        ("<PERSON><PERSON>007", "<PERSON><PERSON> <PERSON><PERSON>n <PERSON>iang", "147 <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> X<PERSON>ch <PERSON>, Qu<PERSON>n Phú Nhuận, TP.HCM", "0666777888"),
        ("KH008", "Đặng Thị Hoa", "258 Đường Lý Thường Kiệt, Quận 11, TP.HCM", "0999000111"),
        ("KH009", "Bùi Văn Inh", "369 Đường Trần Hưng Đạo, Quận 5, TP.HCM", "0222333444"),
        ("KH010", "Lý Thị Kim", "741 Đường Nguyễn Thị Minh Khai, Quận 3, TP.HCM", "0555666777")
    ]
    
    data = []
    base_date = datetime.now() - timedelta(days=30)
    
    for i, (ma_kh, ten_kh, dia_chi, mst) in enumerate(customers, 1):
        # Random số tiền từ 500k đến 3M
        so_tien = random.randint(500000, 3000000)
        thue_gtgt = int(so_tien * 0.1)  # 10% VAT
        tong_tien = so_tien + thue_gtgt
        
        # Random ngày trong tháng trước
        ngay_lap = (base_date + timedelta(days=random.randint(0, 29))).strftime('%Y-%m-%d')
        
        # Nội dung ngẫu nhiên
        noi_dung_list = [
            "Tiền điện tháng 1/2024",
            "Tiền điện tháng 2/2024", 
            "Tiền điện sinh hoạt",
            "Tiền điện kinh doanh",
            "Tiền điện sản xuất"
        ]
        noi_dung = random.choice(noi_dung_list)
        
        # Ghi chú ngẫu nhiên
        ghi_chu_list = ["", "Khách hàng VIP", "Thanh toán đúng hạn", "Khách hàng thường xuyên", ""]
        ghi_chu = random.choice(ghi_chu_list)
        
        data.append({
            'STT': i,
            'Ma_KH': ma_kh,
            'Ten_KH': ten_kh,
            'Dia_Chi': dia_chi,
            'MST': mst,
            'So_Tien': so_tien,
            'Thue_GTGT': thue_gtgt,
            'Tong_Tien': tong_tien,
            'Noi_Dung': noi_dung,
            'Ngay_Lap': ngay_lap,
            'Ghi_Chu': ghi_chu
        })
    
    # Tạo DataFrame
    df = pd.DataFrame(data)
    
    # Lưu ra file Excel
    filename = f"demo_bien_lai_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False, sheet_name='Biên Lai Demo')
    
    print(f"Đã tạo file demo: {filename}")
    print(f"Số lượng biên lai: {len(df)}")
    print(f"Tổng tiền: {df['Tong_Tien'].sum():,} VND")
    
    return filename

if __name__ == "__main__":
    create_demo_excel()
