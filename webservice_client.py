#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VNPT Webservice Client for Electronic Receipt Integration
"""

import requests
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional
import logging
from urllib.parse import urljoin

class VNPTWebserviceClient:
    """Client for VNPT electronic receipt webservices"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or "https://xxxxxxxxxx.vnpt-invoice.com.vn/"
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
    def import_invoices(self,
                       account: str,
                       ac_pass: str,
                       xml_inv_data: str,
                       username: str,
                       password: str,
                       pattern: str = "",
                       serial: str = "",
                       convert: int = 0) -> Dict:
        """
        Import invoices only (without publishing) using VNPT webservice

        Args:
            account: Employee account
            ac_pass: Employee password
            xml_inv_data: Invoice XML data
            username: Customer account
            password: Customer password
            pattern: Invoice pattern (optional)
            serial: Invoice serial (optional)
            convert: TCVN3 conversion (0: No, 1: Yes)

        Returns:
            Response dictionary with status and data
        """
        try:
            # SOAP Web Service endpoint (not REST API)
            base_url = self.base_url.rstrip('/') + '/'
            url = urljoin(base_url, "PublishService.asmx")

            self.logger.debug(f"Calling VNPT Import API: {url}")

            soap_body = self._create_import_soap_request(
                account, ac_pass, xml_inv_data, username, password, pattern, serial, convert
            )

            # Debug: Log SOAP request
            self.logger.debug(f"SOAP Request Headers: {{'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': '\"http://tempuri.org/ImportInv\"'}}")
            self.logger.debug(f"SOAP Request Body: {soap_body}")

            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': '"http://tempuri.org/ImportInv"'
            }

            response = self.session.post(url, data=soap_body, headers=headers, timeout=30)

            # Debug: Log response
            self.logger.debug(f"Response Status: {response.status_code}")
            self.logger.debug(f"Response Headers: {dict(response.headers)}")
            self.logger.debug(f"Response Body: {response.text}")

            response.raise_for_status()

            return {
                'success': True,
                'data': response.text,
                'message': 'Import thành công (chưa publish)'
            }

        except requests.exceptions.HTTPError as e:
            error_msg = str(e)
            if "500 Server Error" in error_msg:
                self.logger.error(f"VNPT Server Error (500): {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_SERVER_ERROR',
                    'error_code': 500,
                    'message': 'Lỗi server VNPT (500). Vui lòng thử lại sau hoặc liên hệ VNPT.',
                    'details': error_msg
                }
            elif "401" in error_msg or "403" in error_msg:
                self.logger.error(f"VNPT Authentication Error: {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_AUTH_ERROR',
                    'error_code': 401,
                    'message': 'Lỗi xác thực VNPT. Vui lòng kiểm tra tài khoản và mật khẩu.',
                    'details': error_msg
                }
            else:
                self.logger.error(f"VNPT HTTP Error: {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_HTTP_ERROR',
                    'message': f'Lỗi kết nối VNPT: {error_msg}',
                    'details': error_msg
                }
        except requests.exceptions.Timeout as e:
            self.logger.error(f"VNPT Timeout Error: {str(e)}")
            return {
                'success': False,
                'error': 'VNPT_TIMEOUT',
                'message': 'Timeout khi kết nối VNPT. Vui lòng thử lại.',
                'details': str(e)
            }
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"VNPT Connection Error: {str(e)}")
            return {
                'success': False,
                'error': 'VNPT_CONNECTION_ERROR',
                'message': 'Không thể kết nối đến server VNPT. Vui lòng kiểm tra mạng.',
                'details': str(e)
            }
        except Exception as e:
            self.logger.error(f"Error importing invoices: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Lỗi không xác định khi import biên lai'
            }

    def import_and_publish_invoices(self,
                                   account: str,
                                   ac_pass: str,
                                   xml_inv_data: str,
                                   username: str,
                                   password: str,
                                   pattern: str = "",
                                   serial: str = "",
                                   convert: int = 0) -> Dict:
        """
        Import and publish invoices using VNPT webservice
        
        Args:
            account: Employee account
            ac_pass: Employee password
            xml_inv_data: Invoice XML data
            username: Customer account
            password: Customer password
            pattern: Invoice pattern (optional)
            serial: Invoice serial (optional)
            convert: TCVN3 conversion (0: No, 1: Yes)
            
        Returns:
            Response dictionary with status and data
        """
        try:
            # SOAP Web Service endpoint (not REST API)
            base_url = self.base_url.rstrip('/') + '/'
            url = urljoin(base_url, "PublishService.asmx")

            self.logger.debug(f"Calling VNPT API: {url}")
            
            # Prepare SOAP request
            soap_body = self._create_publish_soap_request(
                account, ac_pass, xml_inv_data, username, password,
                pattern, serial, convert
            )
            
            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': 'http://tempuri.org/ImportAndPublishInv'
            }
            
            response = self.session.post(url, data=soap_body, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Parse response
            result = self._parse_publish_response(response.text)
            
            self.logger.info(f"Published invoices: {result}")
            return result
            
        except requests.exceptions.HTTPError as e:
            error_msg = str(e)
            if "500 Server Error" in error_msg:
                self.logger.error(f"VNPT Server Error (500): {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_SERVER_ERROR',
                    'error_code': 500,
                    'message': 'Lỗi server VNPT (500). Vui lòng thử lại sau hoặc liên hệ VNPT.',
                    'details': error_msg
                }
            elif "401" in error_msg or "403" in error_msg:
                self.logger.error(f"VNPT Authentication Error: {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_AUTH_ERROR',
                    'error_code': 401,
                    'message': 'Lỗi xác thực VNPT. Vui lòng kiểm tra tài khoản và mật khẩu.',
                    'details': error_msg
                }
            else:
                self.logger.error(f"VNPT HTTP Error: {error_msg}")
                return {
                    'success': False,
                    'error': 'VNPT_HTTP_ERROR',
                    'message': f'Lỗi kết nối VNPT: {error_msg}',
                    'details': error_msg
                }
        except requests.exceptions.Timeout as e:
            self.logger.error(f"VNPT Timeout Error: {str(e)}")
            return {
                'success': False,
                'error': 'VNPT_TIMEOUT',
                'message': 'Timeout khi kết nối VNPT. Vui lòng thử lại.',
                'details': str(e)
            }
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"VNPT Connection Error: {str(e)}")
            return {
                'success': False,
                'error': 'VNPT_CONNECTION_ERROR',
                'message': 'Không thể kết nối đến server VNPT. Vui lòng kiểm tra mạng.',
                'details': str(e)
            }
        except Exception as e:
            self.logger.error(f"Error publishing invoices: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Lỗi không xác định khi xuất biên lai'
            }
            
    def adjust_invoice(self,
                      account: str,
                      ac_pass: str,
                      xml_inv_data: str,
                      username: str,
                      password: str,
                      fkey: str,
                      attach_file: str = "",
                      convert: int = 0) -> Dict:
        """
        Adjust an existing invoice
        
        Args:
            account: Employee account
            ac_pass: Employee password
            xml_inv_data: Adjustment XML data
            username: Customer account
            password: Customer password
            fkey: Invoice key to adjust
            attach_file: Attachment (not used)
            convert: TCVN3 conversion
            
        Returns:
            Response dictionary
        """
        try:
            # SOAP Web Service endpoint
            base_url = self.base_url.rstrip('/') + '/'
            url = urljoin(base_url, "BusinessService.asmx")
            
            soap_body = self._create_adjust_soap_request(
                account, ac_pass, xml_inv_data, username, password,
                fkey, attach_file, convert
            )
            
            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': 'http://tempuri.org/adjustInv'
            }
            
            response = self.session.post(url, data=soap_body, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = self._parse_adjust_response(response.text)
            
            self.logger.info(f"Adjusted invoice: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error adjusting invoice: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to adjust invoice'
            }
            
    def download_invoice_pdf(self,
                           fkey: str,
                           username: str,
                           password: str,
                           require_payment: bool = False) -> Dict:
        """
        Download invoice as PDF
        
        Args:
            fkey: Invoice key
            username: Customer account
            password: Customer password
            require_payment: Whether payment is required
            
        Returns:
            Response dictionary with PDF data
        """
        try:
            # SOAP Web Service endpoint
            base_url = self.base_url.rstrip('/') + '/'
            url = urljoin(base_url, "PortalService.asmx")
            
            # Determine SOAP method based on payment requirement
            service_name = "downloadInvPDFFkey" if require_payment else "downloadInvPDFFkeyNoPay"
            soap_body = self._create_download_soap_request(fkey, username, password, service_name)

            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': f'http://tempuri.org/{service_name}'
            }
            
            response = self.session.post(url, data=soap_body, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = self._parse_download_response(response.text)
            
            self.logger.info(f"Downloaded PDF for invoice {fkey}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error downloading PDF: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to download PDF'
            }

    def _create_import_soap_request(self, account: str, ac_pass: str, xml_inv_data: str,
                                   username: str, password: str, pattern: str,
                                   serial: str, convert: int) -> str:
        """Create SOAP request for ImportInv method (import only, no publish)"""
        soap_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportInv xmlns="http://tempuri.org/">
      <Account>{account}</Account>
      <ACpass>{ac_pass}</ACpass>
      <xmlInvData><![CDATA[{xml_inv_data}]]></xmlInvData>
      <username>{username}</username>
      <password>{password}</password>
      <convert>{convert}</convert>
      <tkTao>{account}</tkTao>
      <pattern>{pattern}</pattern>
      <serial>{serial}</serial>
    </ImportInv>
  </soap:Body>
</soap:Envelope>"""

        return soap_template.format(
            account=account,
            ac_pass=ac_pass,
            xml_inv_data=xml_inv_data,
            username=username,
            password=password,
            pattern=pattern,
            serial=serial,
            convert=convert
        )

    def _create_publish_soap_request(self, account: str, ac_pass: str, xml_inv_data: str,
                                   username: str, password: str, pattern: str,
                                   serial: str, convert: int) -> str:
        """Create SOAP request for publishing invoices"""
        soap_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
               xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ImportAndPublishInv xmlns="http://tempuri.org/">
      <Account>{account}</Account>
      <ACpass>{ac_pass}</ACpass>
      <xmlInvData><![CDATA[{xml_inv_data}]]></xmlInvData>
      <username>{username}</username>
      <password>{password}</password>
      <pattern>{pattern}</pattern>
      <serial>{serial}</serial>
      <convert>{convert}</convert>
    </ImportAndPublishInv>
  </soap:Body>
</soap:Envelope>"""
        
        return soap_template.format(
            account=account,
            ac_pass=ac_pass,
            xml_inv_data=xml_inv_data,
            username=username,
            password=password,
            pattern=pattern,
            serial=serial,
            convert=convert
        )
        
    def _create_adjust_soap_request(self, account: str, ac_pass: str, xml_inv_data: str,
                                  username: str, password: str, fkey: str,
                                  attach_file: str, convert: int) -> str:
        """Create SOAP request for adjusting invoices"""
        soap_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
               xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <adjustInv xmlns="http://tempuri.org/">
      <Account>{account}</Account>
      <ACpass>{ac_pass}</ACpass>
      <xmlInvData><![CDATA[{xml_inv_data}]]></xmlInvData>
      <username>{username}</username>
      <pass>{password}</pass>
      <fkey>{fkey}</fkey>
      <AttachFile>{attach_file}</AttachFile>
      <convert>{convert}</convert>
    </adjustInv>
  </soap:Body>
</soap:Envelope>"""
        
        return soap_template.format(
            account=account,
            ac_pass=ac_pass,
            xml_inv_data=xml_inv_data,
            username=username,
            password=password,
            fkey=fkey,
            attach_file=attach_file,
            convert=convert
        )
        
    def _create_download_soap_request(self, fkey: str, username: str, password: str, service_name: str = "downloadInvPDFFkeyNoPay") -> str:
        """Create SOAP request for downloading PDF"""
        soap_template = """<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{service_name} xmlns="http://tempuri.org/">
      <fkey>{fkey}</fkey>
      <userName>{username}</userName>
      <userPass>{password}</userPass>
    </{service_name}>
  </soap:Body>
</soap:Envelope>"""

        return soap_template.format(
            service_name=service_name,
            fkey=fkey,
            username=username,
            password=password
        )
        
    def _parse_publish_response(self, response_text: str) -> Dict:
        """Parse response from publish service"""
        try:
            # Parse XML response
            root = ET.fromstring(response_text)
            
            # Find result element
            result_elem = root.find('.//{http://tempuri.org/}ImportAndPublishInvResult')
            if result_elem is not None:
                result_text = result_elem.text
                
                if result_text.startswith('OK:'):
                    return {
                        'success': True,
                        'message': 'Invoices published successfully',
                        'data': result_text
                    }
                elif result_text.startswith('ERR:'):
                    return {
                        'success': False,
                        'error': result_text,
                        'message': 'Error publishing invoices'
                    }
                    
            return {
                'success': False,
                'error': 'Unknown response format',
                'message': 'Failed to parse response'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to parse response'
            }
            
    def _parse_adjust_response(self, response_text: str) -> Dict:
        """Parse response from adjust service"""
        # Similar to publish response parsing
        return self._parse_publish_response(response_text)
        
    def _parse_download_response(self, response_text: str) -> Dict:
        """Parse response from download service"""
        try:
            root = ET.fromstring(response_text)
            
            # Find result element (adjust namespace as needed)
            result_elem = root.find('.//{http://tempuri.org/}downloadInvPDFFkeyNoPayResult')
            if result_elem is not None:
                result_text = result_elem.text
                
                if result_text and not result_text.startswith('ERR:'):
                    return {
                        'success': True,
                        'message': 'PDF downloaded successfully',
                        'pdf_data': result_text  # Base64 encoded PDF
                    }
                else:
                    return {
                        'success': False,
                        'error': result_text,
                        'message': 'Error downloading PDF'
                    }
                    
            return {
                'success': False,
                'error': 'No PDF data in response',
                'message': 'Failed to get PDF data'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to parse download response'
            }
            
    def test_connection(self) -> bool:
        """Test connection to VNPT webservice"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            return response.status_code == 200
        except Exception:
            return False

    def test_endpoints(self) -> Dict:
        """Test individual VNPT endpoints"""
        endpoints = [
            "PublishService.asmx",
            "BusinessService.asmx",
            "PortalService.asmx"
        ]

        results = {}
        base_url = self.base_url.rstrip('/') + '/'

        for endpoint in endpoints:
            try:
                endpoint_url = urljoin(base_url, endpoint)
                response = self.session.get(endpoint_url, timeout=5)
                results[endpoint] = {
                    'status': 'accessible' if response.status_code == 200 else f'error_{response.status_code}',
                    'status_code': response.status_code,
                    'url': endpoint_url
                }
            except requests.exceptions.Timeout:
                results[endpoint] = {
                    'status': 'timeout',
                    'status_code': None,
                    'url': endpoint_url
                }
            except Exception as e:
                results[endpoint] = {
                    'status': 'error',
                    'status_code': None,
                    'url': endpoint_url,
                    'error': str(e)
                }

        return results
