#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test các chế độ import khác nhau
"""

import pandas as pd
from datetime import datetime

def create_test_data():
    """Tạo dữ liệu test nhỏ"""
    
    data = {
        'STT': [1, 2],
        'Ma_KH': ['TEST001', 'TEST002'],
        'Ten_KH': ['Khách hàng Test 1', 'Khách hàng Test 2'],
        'Dia_Chi': ['123 Test Street', '456 Demo Avenue'],
        'MST': ['0123456789', '0987654321'],
        'So_Tien': [50000, 75000],
        'Thue_GTGT': [5000, 7500],
        'Tong_Tien': [55000, 82500],
        'Noi_Dung': ['Test import only', 'Test import & publish'],
        'Ngay_Lap': [datetime.now().strftime('%Y-%m-%d')] * 2,
        '<PERSON><PERSON>_<PERSON>': ['Test mode 1', 'Test mode 2']
    }
    
    df = pd.DataFrame(data)
    
    filename = f"test_import_modes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False, sheet_name='Test Data')
    
    print(f"✅ Đã tạo file test: {filename}")
    print(f"📊 Số lượng biên lai: {len(df)}")
    print()
    print("🔧 Hướng dẫn test:")
    print("1. Mở tool GUI")
    print("2. Load file Excel này")
    print("3. Test cả hai chế độ:")
    print("   - 📥 Import Only")
    print("   - 🚀 Import & Publish")
    print("4. So sánh kết quả trong log")
    
    return filename

if __name__ == "__main__":
    create_test_data()
