#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test để kiểm tra tính duy nhất của key cho mỗi hóa đơn
"""

import sys
import os
import pandas as pd
from datetime import datetime
import re

# Thêm thư mục hiện tại vào path để import được module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Tạo class test đơn giản chỉ có method cần thiết
class TestInvoiceTool:
    def __init__(self):
        self._invoice_counter = 0

    def create_invoice_xml(self, row) -> str:
        """Tạo XML data cho biên lai từ dữ liệu row theo format VNPT"""
        # Template XML đúng format VNPT theo tài liệu
        xml_template = """<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>{key}</key>
        <Invoice>
            <CusCode>{cus_code}</CusCode>
            <ArisingDate>{arising_date}</ArisingDate>
            <CusName>{cus_name}</CusName>
            <Buyer>{buyer_name}</Buyer>
            <Total>{total}</Total>
            <Amount>{amount}</Amount>
            <AmountInWords>{amount_words}</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <Note>{note}</Note>
        </Invoice>
    </Inv>
</Invoices>"""

        # Helper function
        def safe_get(value):
            return str(value).strip() if value and str(value).strip() != 'nan' else ''

        # Extract data từ row
        cus_code = safe_get(row.get('Ma_KH', ''))
        cus_name = safe_get(row.get('DonViNop', ''))
        cus_address = safe_get(row.get('Dia_Chi', ''))

        buyer_name = safe_get(row.get('NguoiNopPhi', cus_name))
        buyer_legal_name = buyer_name
        buyer_display_name = buyer_name
        buyer_address = safe_get(row.get('Dia_Chi', ''))
        buyer_tax_code = safe_get(row.get('MST', ''))
        mdvqhnsach = safe_get(row.get('MDVQHNSach', ''))
        cccdan = safe_get(row.get('CCCDan', ''))

        # Ngày tạo biên lai (ArisingDate) - format dd/MM/yyyy theo VNPT
        ngay_lap = row.get('Ngay_Lap', datetime.now().strftime('%Y-%m-%d'))
        if isinstance(ngay_lap, str):
            try:
                # Convert từ YYYY-MM-DD sang dd/MM/yyyy
                date_obj = datetime.strptime(ngay_lap, '%Y-%m-%d')
                arising_date = date_obj.strftime('%d/%m/%Y')
            except:
                arising_date = datetime.now().strftime('%d/%m/%Y')
        else:
            arising_date = datetime.now().strftime('%d/%m/%Y')

        # Tạo key unique - xử lý trường hợp cus_code rỗng
        # Sử dụng timestamp với microseconds để đảm bảo tính duy nhất
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        microseconds = now.strftime('%f')[:3]  # Lấy 3 chữ số đầu của microseconds

        # Thêm counter để đảm bảo tính duy nhất tuyệt đối
        if not hasattr(self, '_invoice_counter'):
            self._invoice_counter = 0
        self._invoice_counter += 1

        unique_suffix = f"{timestamp}{microseconds}{self._invoice_counter:03d}"

        if cus_code and cus_code.strip():
            key = f"{cus_code.strip()}_{unique_suffix}"
        else:
            # Nếu không có mã khách hàng, dùng prefix mặc định
            key = f"VNPT_{unique_suffix}"

        # Các giá trị khác
        amount = int(row.get('So_Tien', 0))
        total = int(row.get('Tong_Tien', amount))
        note = safe_get(row.get('Ghi_Chu', ''))

        # Convert amount to words (simplified)
        amount_words = f"{total:,} đồng"

        return xml_template.format(
            key=key,
            cus_code=cus_code,
            arising_date=arising_date,
            cus_name=cus_name,
            buyer_name=buyer_name,
            total=total,
            amount=amount,
            amount_words=amount_words,
            note=note
        )

def test_unique_keys():
    """Test tính duy nhất của key khi tạo nhiều hóa đơn liên tiếp"""
    
    print("🧪 Test tính duy nhất của key cho mỗi hóa đơn")
    print("=" * 50)
    
    # Tạo dữ liệu test với nhiều hóa đơn
    test_data = []
    for i in range(10):
        test_data.append({
            'STT': i + 1,
            'Ma_KH': f'KH{i+1:03d}' if i % 3 != 0 else '',  # Một số không có mã KH
            'DonViNop': f'Khách hàng {i+1}',
            'Dia_Chi': f'Địa chỉ {i+1}',
            'MST': f'012345678{i}',
            'MDVQHNSach': '',
            'CCCDan': '',
            'NguoiNopPhi': f'Người nộp {i+1}',
            'So_Tien': 100000 + i * 10000,
            'Tong_Tien': 100000 + i * 10000,
            'Noi_Dung': f'Nội dung hóa đơn {i+1}',
            'Ngay_Lap': datetime.now().strftime('%Y-%m-%d'),
            'Ghi_Chu': f'Ghi chú {i+1}'
        })
    
    # Tạo DataFrame
    df = pd.DataFrame(test_data)
    
    # Tạo instance của tool test
    tool = TestInvoiceTool()
    
    # Tạo XML cho từng hóa đơn và thu thập keys
    keys = []
    print("📋 Tạo XML cho từng hóa đơn:")
    print("-" * 30)
    
    for index, row in df.iterrows():
        xml_data = tool.create_invoice_xml(row)
        
        # Extract key từ XML
        key_match = re.search(r'<key>(.*?)</key>', xml_data)
        if key_match:
            key = key_match.group(1)
            keys.append(key)
            print(f"Hóa đơn {index+1:2d}: {key}")
        else:
            print(f"Hóa đơn {index+1:2d}: ❌ Không tìm thấy key")
    
    print("\n" + "=" * 50)
    print("📊 Kết quả kiểm tra:")
    print(f"Tổng số hóa đơn: {len(keys)}")
    print(f"Số key duy nhất: {len(set(keys))}")
    
    if len(keys) == len(set(keys)):
        print("✅ THÀNH CÔNG: Tất cả key đều duy nhất!")
    else:
        print("❌ LỖI: Có key bị trùng lặp!")
        
        # Tìm key trùng lặp
        from collections import Counter
        key_counts = Counter(keys)
        duplicates = {k: v for k, v in key_counts.items() if v > 1}
        
        if duplicates:
            print("\n🔍 Key bị trùng lặp:")
            for key, count in duplicates.items():
                print(f"  - {key}: xuất hiện {count} lần")
    
    print("\n" + "=" * 50)
    print("📝 Mẫu key được tạo:")
    if keys:
        print(f"Key có mã KH: {[k for k in keys if not k.startswith('VNPT_')][:3]}")
        print(f"Key không có mã KH: {[k for k in keys if k.startswith('VNPT_')][:3]}")

if __name__ == "__main__":
    test_unique_keys()
