# -*- mode: python ; coding: utf-8 -*-
import os

block_cipher = None

a = Analysis(
    ['invoice_import_tool.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('vnpt_receipt_integration_docs.md', '.'),
        ('config_sample.json', '.'),
        ('README.md', '.'),
        ('QUICK_START.md', '.'),
        ('LICENSE', '.'),
    ],
    hiddenimports=[
        'ttkbootstrap',
        'ttkbootstrap.constants',
        'ttkbootstrap.scrolled',
        'pandas',
        'openpyxl',
        'requests',
        'lxml',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'webservice_client',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'IPython',
        'jupyter',
        'notebook',
        'PyQt6',
        'PyQt5',
        'PySide2',
        'PySide6',
        'scipy',
        'sklearn',
        'tensorflow',
        'torch',
        'cv2',
        'PIL.ImageQt',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
        'sqlite3',
        'zmq',
        'psutil',
        'win32com',
        'pythoncom',
        'pywintypes',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VNPT_Invoice_Import_Tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # Strip symbols để giảm dung lượng
    upx=True,   # Nén UPX để giảm dung lượng
    upx_exclude=[
        'vcruntime140.dll',
        'msvcp140.dll',
        'api-ms-win-*.dll',
    ],
    runtime_tmpdir=None,
    console=False,  # Không hiển thị console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon=os.path.join(SPECPATH, 'icon.ico'),  # Đường dẫn tuyệt đối đến icon
)
