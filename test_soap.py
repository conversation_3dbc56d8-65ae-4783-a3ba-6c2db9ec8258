#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test SOAP request để debug - sử dụng dữ liệu từ config.json
"""

import logging
import json
import os
import pandas as pd
from webservice_client import VNPTWebserviceClient
from invoice_import_tool import InvoiceImportTool

# Setup logging để thấy debug
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_config():
    """Load cấu hình từ config.json"""
    try:
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print("❌ Không tìm thấy file config.json")
            return None
    except Exception as e:
        print(f"❌ Lỗi đọc config.json: {e}")
        return None

def test_import_soap():
    """Test SOAP request cho ImportInv sử dụng config.json"""

    # Load config
    config = load_config()
    if not config:
        return None

    print("📋 Cấu hình từ config.json:")
    print(f"   URL: {config['vnpt_url']}")
    print(f"   Account: {config['account']}")
    print(f"   Username: {config['username']}")
    print(f"   Pattern: {config['pattern']}")
    print(f"   Serial: {config['serial']}")
    print()

    # Khởi tạo client với URL từ config
    client = VNPTWebserviceClient(config['vnpt_url'])

    # Tạo dữ liệu test row
    test_row = pd.Series({
        'Ma_KH': 'TEST_CONFIG',
        'Ten_KH': 'Khách hàng Test từ Config',
        'Dia_Chi': '123 Test Street, Config City',
        'MST': '**********',
        'So_Tien': 100000,
        'Thue_GTGT': 0,  # Theo VNPT doc, mặc định = 0
        'Tong_Tien': 100000,
        'Noi_Dung': 'Test biên lai từ config',
        'Ngay_Lap': '2024-01-15',
        'Ghi_Chu': 'Test với format VNPT mới'
    })

    # Tạo XML data sử dụng phương thức từ tool
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # Ẩn cửa sổ GUI

    tool = InvoiceImportTool(root)
    xml_data = tool.create_invoice_xml(test_row)

    print("📋 XML Data được tạo:")
    print(xml_data)
    print()

    # Dữ liệu test với thông tin từ config
    test_data = {
        'account': config['account'],
        'ac_pass': config['ac_pass'],
        'xml_inv_data': xml_data,
        'username': config['username'],
        'password': config['password'],
        'pattern': config['pattern'],
        'serial': config['serial'],
        'convert': config['convert']
    }
    
    print("=== Testing ImportInv SOAP Request ===")

    # Test import
    result = client.import_invoices(**test_data)

    print(f"ImportInv Result: {result}")
    print()

    print("=== Testing ImportAndPublishInv SOAP Request ===")

    # Test import and publish
    result2 = client.import_and_publish_invoices(**test_data)

    print(f"ImportAndPublishInv Result: {result2}")

    print()
    print("=== Testing Connection ===")

    # Test basic connection
    connection_ok = client.test_connection()
    print(f"Connection test: {connection_ok}")

    # Test endpoints
    endpoints = client.test_endpoints()
    print(f"Endpoints test: {endpoints}")

    return result, result2

if __name__ == "__main__":
    test_import_soap()
