#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tool Import Biên Lai Điện Tử - GUI Application
Sử dụng ttkbootstrap để tạo giao diện Bootstrap-style import biên lai qua API VNPT

Bản quyền thuộc về Trung tâm CNTT - VNPT Cà Mau
Copyright © 2025 VNPT Ca Mau IT Center. All rights reserved.
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledText

import pandas as pd
import json
import os
import logging
from datetime import datetime
from typing import Dict
import threading
from webservice_client import VNPTWebserviceClient

class InvoiceImportTool:
    """Tool GUI để import biên lai điện tử"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Tool Import Biên Lai Điện Tử - VNPT Cà Mau")
        self.root.geometry("1200x800")

        # Cấu hình logging
        self.setup_logging()

        # Khởi tạo client
        self.client = None

        # Cấu hình mặc định
        self.config = self.load_config()

        # Tạo giao diện
        self.create_widgets()

        # Load cấu hình vào giao diện
        self.load_config_to_ui()

        # Setup các cột mặc định cho bảng dữ liệu
        self.setup_default_columns()

    def get_column_vietnamese_mapping(self):
        """Trả về dictionary mapping tên cột tiếng Anh sang tiếng Việt"""
        return {
            'STT': 'STT',
            'Ma_KH': 'Mã KH',
            'DonViNop': 'Đơn vị nộp',
            'Dia_Chi': 'Địa chỉ',
            'MST': 'Mã số thuế',
            'MDVQHNSach': 'Mã QH ngân sách',
            'CCCDan': 'CCCD',
            'NguoiNopPhi': 'Người nộp phí',
            'So_Tien': 'Số tiền',
            'Tong_Tien': 'Tổng tiền',
            'Noi_Dung': 'Nội dung',
            'Ngay_Lap': 'Ngày lập',
            'Ghi_Chu': 'Ghi chú',
            # Fallback cho các tên cột cũ (backward compatibility)
            'Ten_KH': 'Tên khách hàng',
            'Buyer': 'Người mua',
            'Thue_GTGT': 'Thuế GTGT'
        }

    def setup_default_columns(self):
        """Setup các cột mặc định cho bảng dữ liệu khi chưa load Excel"""
        # Danh sách các cột mặc định
        default_columns = [
            'STT',
            'Ma_KH',
            'DonViNop',
            'Dia_Chi',
            'MST',
            'MDVQHNSach',
            'CCCDan',
            'NguoiNopPhi',
            'So_Tien',
            'Tong_Tien',
            'Noi_Dung',
            'Ngay_Lap',
            'Ghi_Chu'
        ]

        # Setup columns cho Treeview
        self.tree["columns"] = default_columns
        self.tree["show"] = "headings"

        # Configure columns với tên tiếng Việt
        column_vietnamese = self.get_column_vietnamese_mapping()
        for col in default_columns:
            vietnamese_name = column_vietnamese.get(col, col)
            self.tree.heading(col, text=vietnamese_name)
            self.tree.column(col, width=100, minwidth=50)

    def setup_logging(self):
        """Thiết lập logging"""
        logging.basicConfig(
            level=logging.DEBUG,  # Changed to DEBUG to see SOAP requests
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('invoice_import.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> Dict:
        """Load cấu hình từ file"""
        config_file = 'config.json'
        default_config = {
            'vnpt_url': 'https://xxxxxxxxxx.vnpt-invoice.com.vn/',
            'account': '',
            'ac_pass': '',
            'username': '',
            'password': '',
            'pattern': '',
            'serial': '',
            'convert': 0,
            'last_excel_path': '',
            'auto_publish': False
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge với default config để đảm bảo có đủ keys
                    default_config.update(config)
                    return default_config
            else:
                return default_config
        except Exception as e:
            self.logger.error(f"Lỗi load config: {e}")
            return default_config
            
    def save_config(self):
        """Lưu cấu hình ra file"""
        try:
            config = {
                'vnpt_url': self.vnpt_url_var.get(),
                'account': self.account_var.get(),
                'ac_pass': self.ac_pass_var.get(),
                'username': self.username_var.get(),
                'password': self.password_var.get(),
                'pattern': self.pattern_var.get(),
                'serial': self.serial_var.get(),
                'convert': self.convert_var.get(),
                'last_excel_path': self.excel_path_var.get(),
                'auto_publish': self.auto_publish_var.get()
            }
            
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            messagebox.showinfo("💾 Thành công", "Đã lưu cấu hình!")
            self.log_message("💾 Đã lưu cấu hình")
            
        except Exception as e:
            messagebox.showerror("❌ Lỗi", f"Không thể lưu cấu hình: {e}")
            self.log_message(f"❌ Lỗi lưu config: {e}")
            
    def create_widgets(self):
        """Tạo giao diện Bootstrap-style"""
        # Header với title và icon
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill='x', padx=20, pady=(20, 10))

        # Title và copyright
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side='left')

        title_label = ttk.Label(
            title_frame,
            text="⚡ Tool Import Biên Lai Điện Tử VNPT",
            font=("Segoe UI", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(anchor='w')

        copyright_label = ttk.Label(
            title_frame,
            text="© 2025 Trung tâm CNTT - VNPT Cà Mau",
            font=("Segoe UI", 9),
            bootstyle="secondary"
        )
        copyright_label.pack(anchor='w')

        # Status indicator
        self.status_label = ttk.Label(
            header_frame,
            text="● Chưa kết nối",
            font=("Segoe UI", 10),
            bootstyle="danger"
        )
        self.status_label.pack(side='right')

        # Tạo notebook với style đẹp
        self.notebook = ttk.Notebook(self.root, bootstyle="primary")
        self.notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # Tab 1: Cấu hình
        self.create_config_tab()

        # Tab 2: Import Excel
        self.create_import_tab()

        # Tab 3: Log
        self.create_log_tab()

        # Tab 4: Giới thiệu
        self.create_about_tab()
        
    def create_config_tab(self):
        """Tạo tab cấu hình với Bootstrap style"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙️ Cấu hình")

        # Main container với scrollbar
        canvas = tk.Canvas(config_frame)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas và scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y", pady=20)

        # Variables
        self.vnpt_url_var = tk.StringVar()
        self.account_var = tk.StringVar()
        self.ac_pass_var = tk.StringVar()
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.pattern_var = tk.StringVar()
        self.serial_var = tk.StringVar()
        self.convert_var = tk.IntVar()
        self.auto_publish_var = tk.BooleanVar()

        # Cấu hình VNPT với card style
        vnpt_card = ttk.LabelFrame(
            scrollable_frame,
            text="🌐 Cấu hình kết nối VNPT",
            padding=20,
            bootstyle="primary"
        )
        vnpt_card.pack(fill='x', pady=(0, 20))

        # URL VNPT
        ttk.Label(vnpt_card, text="URL VNPT:", font=("Segoe UI", 10, "bold")).grid(row=0, column=0, sticky='w', pady=5)
        url_entry = ttk.Entry(vnpt_card, textvariable=self.vnpt_url_var, width=60, font=("Segoe UI", 10))
        url_entry.grid(row=0, column=1, sticky='ew', pady=5, padx=(10, 0))

        # Account
        ttk.Label(vnpt_card, text="Account:", font=("Segoe UI", 10, "bold")).grid(row=1, column=0, sticky='w', pady=5)
        account_entry = ttk.Entry(vnpt_card, textvariable=self.account_var, width=60, font=("Segoe UI", 10))
        account_entry.grid(row=1, column=1, sticky='ew', pady=5, padx=(10, 0))

        # AC Password
        ttk.Label(vnpt_card, text="AC Password:", font=("Segoe UI", 10, "bold")).grid(row=2, column=0, sticky='w', pady=5)
        ac_pass_entry = ttk.Entry(vnpt_card, textvariable=self.ac_pass_var, show='*', width=60, font=("Segoe UI", 10))
        ac_pass_entry.grid(row=2, column=1, sticky='ew', pady=5, padx=(10, 0))

        # Username
        ttk.Label(vnpt_card, text="Username service:", font=("Segoe UI", 10, "bold")).grid(row=3, column=0, sticky='w', pady=5)
        username_entry = ttk.Entry(vnpt_card, textvariable=self.username_var, width=60, font=("Segoe UI", 10))
        username_entry.grid(row=3, column=1, sticky='ew', pady=5, padx=(10, 0))

        # Password
        ttk.Label(vnpt_card, text="Password service:", font=("Segoe UI", 10, "bold")).grid(row=4, column=0, sticky='w', pady=5)
        password_entry = ttk.Entry(vnpt_card, textvariable=self.password_var, show='*', width=60, font=("Segoe UI", 10))
        password_entry.grid(row=4, column=1, sticky='ew', pady=5, padx=(10, 0))

        vnpt_card.columnconfigure(1, weight=1)

        # Cấu hình bổ sung
        extra_card = ttk.LabelFrame(
            scrollable_frame,
            text="🔧 Cấu hình bổ sung",
            padding=20,
            bootstyle="info"
        )
        extra_card.pack(fill='x', pady=(0, 20))

        # Pattern và Serial trong cùng một row
        pattern_frame = ttk.Frame(extra_card)
        pattern_frame.pack(fill='x', pady=5)

        ttk.Label(pattern_frame, text="Pattern:", font=("Segoe UI", 10, "bold")).pack(side='left')
        pattern_entry = ttk.Entry(pattern_frame, textvariable=self.pattern_var, width=20, font=("Segoe UI", 10))
        pattern_entry.pack(side='left', padx=(10, 20))

        ttk.Label(pattern_frame, text="Serial:", font=("Segoe UI", 10, "bold")).pack(side='left')
        serial_entry = ttk.Entry(pattern_frame, textvariable=self.serial_var, width=20, font=("Segoe UI", 10))
        serial_entry.pack(side='left', padx=(10, 0))

        # Convert
        convert_frame = ttk.Frame(extra_card)
        convert_frame.pack(fill='x', pady=5)

        ttk.Label(convert_frame, text="Convert TCVN3:", font=("Segoe UI", 10, "bold")).pack(side='left')
        convert_combo = ttk.Combobox(
            convert_frame,
            textvariable=self.convert_var,
            values=[0, 1],
            width=10,
            state="readonly",
            font=("Segoe UI", 10)
        )
        convert_combo.pack(side='left', padx=(10, 0))

        # Auto publish checkbox (chỉ ảnh hưởng đến nút cũ)
        auto_publish_check = ttk.Checkbutton(
            extra_card,
            text="🔄 Auto publish (cho backward compatibility)",
            variable=self.auto_publish_var,
            bootstyle="warning-round-toggle"
        )
        auto_publish_check.pack(pady=10)

        # Thông tin về các nút mới
        info_label = ttk.Label(
            extra_card,
            text="💡 Sử dụng nút '📥 Import Only' hoặc '🚀 Import & Publish' để chọn chế độ",
            font=("Segoe UI", 9),
            bootstyle="info"
        )
        info_label.pack(pady=(0, 5))

        # Action buttons
        button_card = ttk.Frame(scrollable_frame)
        button_card.pack(fill='x', pady=10)

        test_btn = ttk.Button(
            button_card,
            text="🔍 Test Kết Nối",
            command=self.test_connection,
            bootstyle="info-outline",
            width=15
        )
        test_btn.pack(side='left', padx=(0, 10))

        save_btn = ttk.Button(
            button_card,
            text="💾 Lưu Cấu Hình",
            command=self.save_config,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side='left')
        
    def create_import_tab(self):
        """Tạo tab import với Bootstrap style"""
        import_frame = ttk.Frame(self.notebook)
        self.notebook.add(import_frame, text="📊 Import Excel")

        # Main container
        main_container = ttk.Frame(import_frame)
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # File selection card
        file_card = ttk.LabelFrame(
            main_container,
            text="📁 Chọn file Excel",
            padding=20,
            bootstyle="warning"
        )
        file_card.pack(fill='x', pady=(0, 20))

        self.excel_path_var = tk.StringVar()

        # File path entry
        path_frame = ttk.Frame(file_card)
        path_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(path_frame, text="Đường dẫn file:", font=("Segoe UI", 10, "bold")).pack(anchor='w')
        ttk.Label(path_frame, text="💡 File sẽ được tự động load sau khi chọn",
                 font=("Segoe UI", 9), bootstyle="info").pack(anchor='w', pady=(2, 0))

        file_input_frame = ttk.Frame(path_frame)
        file_input_frame.pack(fill='x', pady=(5, 0))

        path_entry = ttk.Entry(
            file_input_frame,
            textvariable=self.excel_path_var,
            font=("Segoe UI", 10),
            state="readonly"
        )
        path_entry.pack(side='left', fill='x', expand=True)

        # File action buttons
        button_frame = ttk.Frame(file_input_frame)
        button_frame.pack(side='right', padx=(10, 0))

        create_btn = ttk.Button(
            button_frame,
            text="📝 Tạo Mẫu",
            command=self.create_sample_excel,
            bootstyle="info-outline",
            width=12
        )
        create_btn.pack(side='left', padx=(0, 5))

        select_btn = ttk.Button(
            button_frame,
            text="📂 Chọn File",
            command=self.select_excel_file,
            bootstyle="primary",
            width=12
        )
        select_btn.pack(side='left')

        # Preview card
        preview_card = ttk.LabelFrame(
            main_container,
            text="👁️ Xem trước dữ liệu",
            padding=20,
            bootstyle="secondary"
        )
        preview_card.pack(fill='both', expand=True, pady=(0, 20))

        # Treeview với scrollbar
        tree_frame = ttk.Frame(preview_card)
        tree_frame.pack(fill='both', expand=True)

        self.tree = ttk.Treeview(tree_frame, bootstyle="info")
        tree_scroll_y = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        tree_scroll_x = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)

        self.tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)

        self.tree.grid(row=0, column=0, sticky='nsew')
        tree_scroll_y.grid(row=0, column=1, sticky='ns')
        tree_scroll_x.grid(row=1, column=0, sticky='ew')

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Action buttons và progress
        action_card = ttk.Frame(main_container)
        action_card.pack(fill='x')

        # Left buttons
        left_buttons = ttk.Frame(action_card)
        left_buttons.pack(side='left')

        refresh_btn = ttk.Button(
            left_buttons,
            text="Làm mới",
            command=self.load_excel,
            bootstyle="info-outline",
            width=12
        )
        refresh_btn.pack(side='left', padx=(0, 10))

        import_btn = ttk.Button(
            left_buttons,
            text="📥 Import",
            command=self.import_invoices_only,
            bootstyle="info",
            width=15
        )
        import_btn.pack(side='left', padx=(0, 5))

        import_publish_btn = ttk.Button(
            left_buttons,
            text="🚀 Phát hành",
            command=self.import_and_publish_invoices,
            bootstyle="success",
            width=18
        )
        import_publish_btn.pack(side='left')

        # Progress bar
        progress_frame = ttk.Frame(action_card)
        progress_frame.pack(side='right', fill='x', expand=True, padx=(20, 0))

        ttk.Label(progress_frame, text="Tiến độ:", font=("Segoe UI", 10, "bold")).pack(anchor='w')

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            bootstyle="success-striped",
            length=300
        )
        self.progress_bar.pack(fill='x', pady=(5, 0))
        
    def create_log_tab(self):
        """Tạo tab log với Bootstrap style"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📋 Log")

        # Main container
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Log header
        log_header = ttk.Frame(log_container)
        log_header.pack(fill='x', pady=(0, 10))

        ttk.Label(
            log_header,
            text="📝 Log hoạt động",
            font=("Segoe UI", 14, "bold"),
            bootstyle="primary"
        ).pack(side='left')

        clear_btn = ttk.Button(
            log_header,
            text="🗑️ Xóa Log",
            command=self.clear_log,
            bootstyle="danger-outline",
            width=12
        )
        clear_btn.pack(side='right')

        # Log text area với ScrolledText
        self.log_text = ScrolledText(
            log_container,
            wrap=tk.WORD,
            height=25,
            font=("Consolas", 10),
            bootstyle="secondary"
        )
        self.log_text.pack(fill='both', expand=True)

    def create_about_tab(self):
        """Tạo tab giới thiệu với thông tin bản quyền"""
        about_frame = ttk.Frame(self.notebook)
        self.notebook.add(about_frame, text="ℹ️ Giới thiệu")

        # Main container
        about_container = ttk.Frame(about_frame)
        about_container.pack(fill='both', expand=True, padx=40, pady=40)

        # Logo và title
        title_section = ttk.Frame(about_container)
        title_section.pack(fill='x', pady=(0, 30))

        app_title = ttk.Label(
            title_section,
            text="⚡ Tool Import Biên Lai Điện Tử VNPT",
            font=("Segoe UI", 20, "bold"),
            bootstyle="primary"
        )
        app_title.pack()

        version_label = ttk.Label(
            title_section,
            text="Phiên bản 1.0",
            font=("Segoe UI", 12),
            bootstyle="info"
        )
        version_label.pack(pady=(5, 0))

        # Thông tin bản quyền
        copyright_section = ttk.LabelFrame(
            about_container,
            text="📋 Thông tin bản quyền",
            padding=20,
            bootstyle="warning"
        )
        copyright_section.pack(fill='x', pady=(0, 20))

        copyright_text = """
🏢 Bản quyền thuộc về: Trung tâm CNTT - VNPT Cà Mau
📅 Năm phát hành: 2025
📧 Liên hệ hỗ trợ: <EMAIL>
🌐 Website: https://vnpt-camau.vn
        """

        copyright_label = ttk.Label(
            copyright_section,
            text=copyright_text.strip(),
            font=("Segoe UI", 10),
            justify="left"
        )
        copyright_label.pack(anchor='w')

        # Thông tin kỹ thuật
        tech_section = ttk.LabelFrame(
            about_container,
            text="🔧 Thông tin kỹ thuật",
            padding=20,
            bootstyle="info"
        )
        # tech_section.pack(fill='x', pady=(0, 20))

        tech_text = """
🐍 Ngôn ngữ: Python 3.8+
🎨 Giao diện: ttkbootstrap (Bootstrap-style)
📊 Xử lý dữ liệu: pandas, openpyxl
🌐 API Integration: VNPT Invoice WebService
📋 Logging: Python logging module
🔒 Bảo mật: HTTPS, Authentication 2-layer
        """

        # tech_label = ttk.Label(
        #     tech_section,
        #     text=tech_text.strip(),
        #     font=("Segoe UI", 10),
        #     justify="left"
        # )
        # tech_label.pack(anchor='w')

        # Tính năng
        features_section = ttk.LabelFrame(
            about_container,
            text="✨ Tính năng chính",
            padding=20,
            bootstyle="success"
        )
        features_section.pack(fill='x')

        features_text = """
📥 Import biên lai từ file Excel
🚀 Import và Publish biên lai tự động
👁️ Preview dữ liệu trước khi import
⚙️ Cấu hình và lưu trữ thông tin kết nối
📊 Progress tracking và báo cáo chi tiết
📋 Log hoạt động real-time
🎨 Giao diện Bootstrap hiện đại
🔄 Hỗ trợ batch processing (tối đa 5000 biên lai)
        """

        features_label = ttk.Label(
            features_section,
            text=features_text.strip(),
            font=("Segoe UI", 10),
            justify="left"
        )
        features_label.pack(anchor='w')

    def load_config_to_ui(self):
        """Load cấu hình vào giao diện"""
        self.vnpt_url_var.set(self.config.get('vnpt_url', ''))
        self.account_var.set(self.config.get('account', ''))
        self.ac_pass_var.set(self.config.get('ac_pass', ''))
        self.username_var.set(self.config.get('username', ''))
        self.password_var.set(self.config.get('password', ''))
        self.pattern_var.set(self.config.get('pattern', ''))
        self.serial_var.set(self.config.get('serial', ''))
        self.convert_var.set(self.config.get('convert', 0))
        self.excel_path_var.set(self.config.get('last_excel_path', ''))
        self.auto_publish_var.set(self.config.get('auto_publish', False))

    def test_connection(self):
        """Test kết nối đến VNPT với Bootstrap notifications"""
        try:
            url = self.vnpt_url_var.get().strip()
            if not url:
                messagebox.showerror("❌ Lỗi", "Vui lòng nhập URL VNPT")
                return

            # Update status to testing
            self.status_label.config(text="🔄 Đang test...", bootstyle="warning")
            self.root.update()

            self.client = VNPTWebserviceClient(url)

            # Test connection
            if self.client.test_connection():
                # Update status to connected
                self.status_label.config(text="✅ Đã kết nối", bootstyle="success")

                messagebox.showinfo("✅ Thành công", "Kết nối VNPT thành công!")
                self.log_message("✅ Kết nối VNPT thành công")

                # Test endpoints
                endpoints_result = self.client.test_endpoints()
                self.log_message(f"📊 Kết quả test endpoints: {endpoints_result}")
            else:
                # Update status to error
                self.status_label.config(text="❌ Lỗi kết nối", bootstyle="danger")

                messagebox.showerror("❌ Lỗi", "Không thể kết nối đến VNPT")
                self.log_message("❌ Không thể kết nối đến VNPT")

        except Exception as e:
            # Update status to error
            self.status_label.config(text="❌ Lỗi kết nối", bootstyle="danger")

            messagebox.showerror("❌ Lỗi", f"Lỗi test kết nối: {e}")
            self.log_message(f"❌ Lỗi test kết nối: {e}")

    def create_sample_excel(self):
        """Tạo file Excel mẫu chỉ có header"""
        try:
            # Tạo DataFrame chỉ có header, không có dữ liệu mẫu
            columns = [
                'STT',
                'Ma_KH',
                'DonViNop',
                'Dia_Chi',
                'MST',
                'MDVQHNSach',
                'CCCDan',
                'NguoiNopPhi',
                'So_Tien',
                'Tong_Tien',
                'Noi_Dung',
                'Ngay_Lap',
                'Ghi_Chu'
            ]

            # Tạo DataFrame rỗng chỉ có header
            df = pd.DataFrame(columns=columns)

            # Chọn nơi lưu file
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Lưu file Excel mẫu"
            )

            if file_path:
                df.to_excel(file_path, index=False, sheet_name='Biên Lai')
                messagebox.showinfo("📝 Thành công", f"Đã tạo file mẫu: {file_path}")
                self.log_message(f"📝 Đã tạo file Excel mẫu: {file_path}")

                # Set path vào ô input và tự động load
                self.excel_path_var.set(file_path)
                self.load_excel()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tạo file mẫu: {e}")
            self.log_message(f"Lỗi tạo file mẫu: {e}")

    def select_excel_file(self):
        """Chọn file Excel và tự động load nội dung"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")],
            title="Chọn file Excel"
        )

        if file_path:
            self.excel_path_var.set(file_path)
            self.log_message(f"📂 Đã chọn file: {file_path}")

            # Tự động load nội dung file Excel
            self.load_excel()

    def load_excel(self):
        """Load dữ liệu từ Excel và hiển thị preview"""
        try:
            file_path = self.excel_path_var.get().strip()
            if not file_path:
                messagebox.showwarning("⚠️ Cảnh báo", "Vui lòng chọn file Excel trước")
                return
            if not os.path.exists(file_path):
                messagebox.showerror("❌ Lỗi", f"File không tồn tại: {file_path}")
                return

            # Đọc Excel
            df = pd.read_excel(file_path)

            # Thay thế NaN bằng chuỗi rỗng
            df = df.fillna('')

            # Clear treeview
            for item in self.tree.get_children():
                self.tree.delete(item)

            # Setup columns
            self.tree["columns"] = list(df.columns)
            self.tree["show"] = "headings"

            # Configure columns với tên tiếng Việt
            column_vietnamese = self.get_column_vietnamese_mapping()
            for col in df.columns:
                # Sử dụng tên tiếng Việt nếu có, nếu không thì giữ nguyên
                vietnamese_name = column_vietnamese.get(col, col)
                self.tree.heading(col, text=vietnamese_name)
                self.tree.column(col, width=100, minwidth=50)

            # Insert data (đã được thay thế NaN bằng chuỗi rỗng)
            for _, row in df.iterrows():
                self.tree.insert("", "end", values=list(row))

            self.log_message(f"Đã load {len(df)} dòng dữ liệu từ Excel")
            messagebox.showinfo("📊 Thành công", f"Đã load {len(df)} dòng dữ liệu")

            # Lưu dataframe để sử dụng sau
            self.current_df = df

        except Exception as e:
            messagebox.showerror("❌ Lỗi", f"Không thể đọc file Excel: {e}")
            self.log_message(f"❌ Lỗi đọc Excel: {e}")

    def log_message(self, message: str):
        """Thêm message vào log"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Log vào file
        self.logger.info(message)

    def clear_log(self):
        """Xóa log"""
        self.log_text.delete(1.0, tk.END)

    def import_invoices_only(self):
        """Import biên lai từ Excel (chỉ import, không publish)"""
        self._import_invoices_with_mode(import_only=True)

    def import_and_publish_invoices(self):
        """Import và publish biên lai từ Excel"""
        self._import_invoices_with_mode(import_only=False)

    def _import_invoices_with_mode(self, import_only: bool = True):
        """Import biên lai với mode được chỉ định"""
        try:
            # Kiểm tra dữ liệu
            if not hasattr(self, 'current_df') or self.current_df is None:
                messagebox.showerror("❌ Lỗi", "Vui lòng load dữ liệu Excel trước")
                return

            # Kiểm tra cấu hình
            if not self.validate_config():
                return

            # Khởi tạo client
            self.client = VNPTWebserviceClient(self.vnpt_url_var.get().strip())

            # Confirm import
            action = "import only" if import_only else "import và publish"
            icon = "📥" if import_only else "🚀"

            result = messagebox.askyesno(
                f"{icon} Xác nhận",
                f"Bạn có muốn {action} {len(self.current_df)} biên lai không?"
            )

            if not result:
                return

            # Disable button và hiển thị progress
            self.progress_var.set(0)

            # Lưu mode để worker sử dụng
            self.current_import_mode = import_only

            # Chạy import trong thread riêng
            import_thread = threading.Thread(target=self._import_worker)
            import_thread.daemon = True
            import_thread.start()

        except Exception as e:
            messagebox.showerror("❌ Lỗi", f"Lỗi import: {e}")
            self.log_message(f"❌ Lỗi import: {e}")

    # Backward compatibility - giữ phương thức cũ
    def import_invoices(self):
        """Import biên lai từ Excel (backward compatibility)"""
        # Sử dụng auto_publish setting từ config
        auto_publish = self.auto_publish_var.get()
        self._import_invoices_with_mode(import_only=not auto_publish)

    def validate_config(self) -> bool:
        """Validate cấu hình trước khi import"""
        required_fields = {
            'vnpt_url': self.vnpt_url_var.get().strip(),
            'account': self.account_var.get().strip(),
            'ac_pass': self.ac_pass_var.get().strip(),
            'username': self.username_var.get().strip(),
            'password': self.password_var.get().strip()
        }

        for field, value in required_fields.items():
            if not value:
                messagebox.showerror("Lỗi", f"Vui lòng nhập {field}")
                return False

        return True

    def _import_worker(self):
        """Worker thread cho import"""
        try:
            total_rows = len(self.current_df)
            success_count = 0
            error_count = 0

            # Sử dụng mode được chỉ định
            import_only = getattr(self, 'current_import_mode', True)
            action_name = "Import only" if import_only else "Import & Publish"

            self.log_message(f"🚀 Bắt đầu {action_name} {total_rows} biên lai...")

            for index, row in self.current_df.iterrows():
                try:
                    # Tạo XML data từ row
                    xml_data = self.create_invoice_xml(row)

                    # Import hoặc import + publish
                    if import_only:
                        # Chỉ import, không publish
                        result = self.client.import_invoices(
                            account=self.account_var.get().strip(),
                            ac_pass=self.ac_pass_var.get().strip(),
                            xml_inv_data=xml_data,
                            username=self.username_var.get().strip(),
                            password=self.password_var.get().strip(),
                            pattern=self.pattern_var.get().strip(),
                            serial=self.serial_var.get().strip(),
                            convert=self.convert_var.get()
                        )
                    else:
                        # Import và publish
                        result = self.client.import_and_publish_invoices(
                            account=self.account_var.get().strip(),
                            ac_pass=self.ac_pass_var.get().strip(),
                            xml_inv_data=xml_data,
                            username=self.username_var.get().strip(),
                            password=self.password_var.get().strip(),
                            pattern=self.pattern_var.get().strip(),
                            serial=self.serial_var.get().strip(),
                            convert=self.convert_var.get()
                        )

                    if result.get('success'):
                        success_count += 1
                        self.log_message(f"Thành công dòng {index + 1}: {row.get('Ten_KH', 'N/A')}")
                    else:
                        error_count += 1
                        error_msg = result.get('message', 'Unknown error')
                        self.log_message(f"Lỗi dòng {index + 1}: {error_msg}")

                except Exception as e:
                    error_count += 1
                    self.log_message(f"Lỗi dòng {index + 1}: {e}")

                # Update progress
                progress = ((index + 1) / total_rows) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

            # Hoàn thành
            self.root.after(0, lambda: self._import_completed(success_count, error_count, total_rows))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Lỗi import worker: {e}"))
            self.log_message(f"Lỗi import worker: {e}")

    def _import_completed(self, success_count: int, error_count: int, total_rows: int):
        """Xử lý khi import hoàn thành"""
        self.progress_var.set(100)

        # Lấy thông tin mode đã sử dụng
        import_only = getattr(self, 'current_import_mode', True)
        action_name = "Import only" if import_only else "Import & Publish"

        message = f"{action_name} hoàn thành!\n"
        message += f"Tổng: {total_rows}\n"
        message += f"Thành công: {success_count}\n"
        message += f"Lỗi: {error_count}"

        if success_count > 0:
            if import_only:
                message += f"\n\n💡 Tip: Biên lai đã được import nhưng chưa publish."
            else:
                message += f"\n\n✅ Biên lai đã được import và publish thành công!"

        messagebox.showinfo(f"🎉 Hoàn thành", message)
        self.log_message(f"🎉 {action_name} hoàn thành: {success_count}/{total_rows} thành công")

    def create_invoice_xml(self, row) -> str:
        """Tạo XML data cho biên lai từ dữ liệu row theo format VNPT"""
        # Template XML đúng format VNPT theo tài liệu
        xml_template = """<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>{key}</key>
        <Invoice>
            <CusCode>{cus_code}</CusCode>
            <ArisingDate>{arising_date}</ArisingDate>
            <CusName>{cus_name}</CusName>
            <Buyer>{buyer_name}</Buyer>
            <Total>{total}</Total>
            <Amount>{amount}</Amount>
            <AmountInWords>{amount_words}</AmountInWords>
            <VATAmount>0</VATAmount>
            <VATRate>0</VATRate>
            <CusAddress>{cus_address}</CusAddress>
            <MDVQHNSach>{mdvqhnsach}</MDVQHNSach>
            <CCCDan>{cccdan}</CCCDan>
            <PaymentMethod>TM</PaymentMethod>
            <Extra>{note}</Extra>
            <Products>
                <Product>
                    <Code></Code>
                    <ProdName>{prod_name}</ProdName>
                    <ProdUnit>lần</ProdUnit>
                    <ProdQuantity>1</ProdQuantity>
                    <ProdPrice>{amount}</ProdPrice>
                    <Total>{amount}</Total>
                    <Amount>{amount}</Amount>
                    <VATRate>0</VATRate>
                </Product>
            </Products>
        </Invoice>
    </Inv>
</Invoices>"""

        # Hàm helper để xử lý NaN và None
        def safe_get(value, default=''):
            """Lấy giá trị an toàn, thay thế NaN/None bằng default"""
            if pd.isna(value) or value is None:
                return default
            return str(value).strip()

        def safe_get_float(value, default=0):
            """Lấy giá trị float an toàn, thay thế NaN/None bằng default"""
            if pd.isna(value) or value is None:
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                return default

        # Lấy dữ liệu từ row với xử lý NaN
        cus_code = safe_get(row.get('Ma_KH', ''))
        cus_name = safe_get(row.get('DonViNop', ''))  # Đổi từ Ten_KH thành DonViNop
        cus_address = safe_get(row.get('Dia_Chi', ''))
        amount = safe_get_float(row.get('So_Tien', 0))
        total = safe_get_float(row.get('Tong_Tien', amount))
        prod_name = safe_get(row.get('Noi_Dung', 'Tiền điện'))
        note = safe_get(row.get('Ghi_Chu', ''))

        # Thông tin Buyer (Người nộp phí)
        buyer_name = safe_get(row.get('NguoiNopPhi', row.get('DonViNop', '')))  # Đổi từ Buyer thành NguoiNopPhi
        buyer_legal_name = buyer_name
        buyer_display_name = buyer_name
        buyer_address = safe_get(row.get('Dia_Chi', ''))  # Mặc định = Dia_Chi
        buyer_tax_code = safe_get(row.get('MST', ''))  # Mặc định = MST
        mdvqhnsach = safe_get(row.get('MDVQHNSach', ''))  # Mã quan hệ ngân sách
        cccdan = safe_get(row.get('CCCDan', ''))  # Căn cước công dân

        # Ngày tạo biên lai (ArisingDate) - format dd/MM/yyyy theo VNPT
        ngay_lap = row.get('Ngay_Lap', datetime.now().strftime('%Y-%m-%d'))
        if isinstance(ngay_lap, str):
            try:
                # Convert từ YYYY-MM-DD sang dd/MM/yyyy
                date_obj = datetime.strptime(ngay_lap, '%Y-%m-%d')
                arising_date = date_obj.strftime('%d/%m/%Y')
            except:
                arising_date = datetime.now().strftime('%d/%m/%Y')
        else:
            arising_date = datetime.now().strftime('%d/%m/%Y')

        # Tạo key unique - xử lý trường hợp cus_code rỗng
        # Sử dụng timestamp với microseconds để đảm bảo tính duy nhất
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        microseconds = now.strftime('%f')[:3]  # Lấy 3 chữ số đầu của microseconds

        # Thêm counter để đảm bảo tính duy nhất tuyệt đối
        if not hasattr(self, '_invoice_counter'):
            self._invoice_counter = 0
        self._invoice_counter += 1

        unique_suffix = f"{timestamp}{microseconds}{self._invoice_counter:03d}"

        if cus_code and cus_code.strip():
            key = f"{cus_code.strip()}_{unique_suffix}"
        else:
            # Nếu không có mã khách hàng, dùng prefix mặc định
            key = f"VNPT_{unique_suffix}"

        # Convert amount to words (simplified)
        amount_words = self.number_to_words(int(total))

        # VAT rate (mặc định 0 - không có thuế GTGT)
        vat_rate = 0
        vat_amount = 0

        return xml_template.format(
            key=key,
            cus_code=cus_code,
            arising_date=arising_date,
            cus_name=cus_name,
            cus_address=cus_address,
            buyer_name=buyer_name,
            buyer_legal_name=buyer_legal_name,
            buyer_display_name=buyer_display_name,
            buyer_address=buyer_address,
            buyer_tax_code=buyer_tax_code,
            mdvqhnsach=mdvqhnsach,
            cccdan=cccdan,
            prod_name=prod_name,
            amount=int(amount),
            vat_amount=int(vat_amount),
            vat_rate=vat_rate,
            total=int(total),
            amount_words=amount_words,
            note=note
        )

    def number_to_words(self, number: int) -> str:
        """Chuyển số thành chữ tiếng Việt"""
        if number == 0:
            return "Không đồng"

        # Các từ số cơ bản
        ones = ["", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"]
        tens = ["", "", "hai mươi", "ba mươi", "bốn mươi", "năm mươi",
                "sáu mươi", "bảy mươi", "tám mươi", "chín mươi"]

        def convert_hundreds(n):
            """Chuyển đổi số từ 0-999"""
            if n == 0:
                return ""

            result = ""

            # Hàng trăm
            if n >= 100:
                result += ones[n // 100] + " trăm"
                n %= 100
                if n > 0:
                    result += " "

            # Hàng chục và đơn vị
            if n >= 20:
                result += tens[n // 10]
                if n % 10 > 0:
                    result += " " + ones[n % 10]
            elif n >= 10:
                if n == 10:
                    result += "mười"
                else:
                    result += "mười " + ones[n % 10]
            elif n > 0:
                result += ones[n]

            return result

        def convert_group(n):
            """Chuyển đổi nhóm 3 chữ số"""
            if n == 0:
                return ""

            # Xử lý trường hợp đặc biệt
            if n < 10:
                return ones[n]
            elif n < 20:
                if n == 10:
                    return "mười"
                else:
                    return "mười " + ones[n % 10]
            elif n < 100:
                result = tens[n // 10]
                if n % 10 > 0:
                    if n % 10 == 1 and n > 10:
                        result += " mốt"  # 21 -> hai mươi mốt
                    else:
                        result += " " + ones[n % 10]
                return result
            else:
                return convert_hundreds(n)

        # Xử lý số âm
        if number < 0:
            return "Âm " + self.number_to_words(-number)

        # Các đơn vị lớn
        units = ["", "nghìn", "triệu", "tỷ"]

        # Chia số thành các nhóm 3 chữ số
        groups = []
        while number > 0:
            groups.append(number % 1000)
            number //= 1000

        # Chuyển đổi từng nhóm
        result_parts = []
        for i, group in enumerate(reversed(groups)):
            if group > 0:
                group_text = convert_group(group)
                unit_index = len(groups) - 1 - i
                if unit_index > 0 and unit_index < len(units):
                    group_text += " " + units[unit_index]
                result_parts.append(group_text)

        result = " ".join(result_parts)

        # Chuẩn hóa kết quả
        result = result.strip()
        if result:
            # Viết hoa chữ cái đầu
            result = result[0].upper() + result[1:] if len(result) > 1 else result.upper()
            result += " đồng"
        else:
            result = "Không đồng"

        return result


def main():
    """Hàm main với Bootstrap theme"""
    # Tạo app với theme Bootstrap
    app = ttk.Window(
        title="⚡ Tool Import Biên Lai Điện Tử - VNPT © 2025 VNPT Cà Mau",
        themename="cosmo",  # Theme Bootstrap đẹp
        size=(1200, 800),
        resizable=(True, True)
    )

    # Set icon nếu có
    try:
        app.iconbitmap("icon.ico")  # Nếu có file icon
    except:
        pass

    # Khởi tạo tool
    InvoiceImportTool(app)

    # Chạy app
    app.mainloop()


if __name__ == "__main__":
    main()
