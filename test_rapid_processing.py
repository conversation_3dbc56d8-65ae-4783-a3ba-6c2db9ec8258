#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test để kiểm tra tính duy nhất của key khi xử lý nhanh nhiều hóa đơn
"""

import time
import pandas as pd
from datetime import datetime
import re

# Tạo class test đơn giản chỉ có method cần thiết
class TestInvoiceTool:
    def __init__(self):
        self._invoice_counter = 0
    
    def create_invoice_xml(self, row) -> str:
        """Tạo XML data cho biên lai từ dữ liệu row theo format VNPT"""
        xml_template = """<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>{key}</key>
        <Invoice>
            <CusCode>{cus_code}</CusCode>
            <Total>{total}</Total>
        </Invoice>
    </Inv>
</Invoices>"""

        # Helper function
        def safe_get(value):
            return str(value).strip() if value and str(value).strip() != 'nan' else ''

        # Extract data từ row
        cus_code = safe_get(row.get('Ma_KH', ''))
        
        # Tạo key unique - xử lý trường hợp cus_code rỗng
        # Sử dụng timestamp với microseconds để đảm bảo tính duy nhất
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        microseconds = now.strftime('%f')[:3]  # Lấy 3 chữ số đầu của microseconds
        
        # Thêm counter để đảm bảo tính duy nhất tuyệt đối
        if not hasattr(self, '_invoice_counter'):
            self._invoice_counter = 0
        self._invoice_counter += 1
        
        unique_suffix = f"{timestamp}{microseconds}{self._invoice_counter:03d}"
        
        if cus_code and cus_code.strip():
            key = f"{cus_code.strip()}_{unique_suffix}"
        else:
            # Nếu không có mã khách hàng, dùng prefix mặc định
            key = f"VNPT_{unique_suffix}"

        total = int(row.get('Tong_Tien', 100000))

        return xml_template.format(
            key=key,
            cus_code=cus_code,
            total=total
        )

def test_rapid_processing():
    """Test xử lý nhanh nhiều hóa đơn liên tiếp"""
    
    print("🚀 Test xử lý nhanh nhiều hóa đơn liên tiếp")
    print("=" * 60)
    
    # Tạo dữ liệu test với 100 hóa đơn
    test_data = []
    for i in range(100):
        test_data.append({
            'STT': i + 1,
            'Ma_KH': f'KH{i+1:03d}' if i % 5 != 0 else '',  # 20% không có mã KH
            'DonViNop': f'Khách hàng {i+1}',
            'Tong_Tien': 100000 + i * 1000,
        })
    
    # Tạo DataFrame
    df = pd.DataFrame(test_data)
    
    # Tạo instance của tool test
    tool = TestInvoiceTool()
    
    # Đo thời gian xử lý
    start_time = time.time()
    
    # Tạo XML cho từng hóa đơn và thu thập keys
    keys = []
    print("⚡ Xử lý nhanh 100 hóa đơn...")
    
    for index, row in df.iterrows():
        xml_data = tool.create_invoice_xml(row)
        
        # Extract key từ XML
        key_match = re.search(r'<key>(.*?)</key>', xml_data)
        if key_match:
            key = key_match.group(1)
            keys.append(key)
        
        # In progress mỗi 20 hóa đơn
        if (index + 1) % 20 == 0:
            print(f"  Đã xử lý {index + 1}/100 hóa đơn...")
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n⏱️  Thời gian xử lý: {processing_time:.3f} giây")
    print(f"📈 Tốc độ: {len(keys)/processing_time:.1f} hóa đơn/giây")
    
    print("\n" + "=" * 60)
    print("📊 Kết quả kiểm tra:")
    print(f"Tổng số hóa đơn: {len(keys)}")
    print(f"Số key duy nhất: {len(set(keys))}")
    
    if len(keys) == len(set(keys)):
        print("✅ THÀNH CÔNG: Tất cả key đều duy nhất!")
    else:
        print("❌ LỖI: Có key bị trùng lặp!")
        
        # Tìm key trùng lặp
        from collections import Counter
        key_counts = Counter(keys)
        duplicates = {k: v for k, v in key_counts.items() if v > 1}
        
        if duplicates:
            print("\n🔍 Key bị trùng lặp:")
            for key, count in duplicates.items():
                print(f"  - {key}: xuất hiện {count} lần")
    
    print("\n" + "=" * 60)
    print("📝 Mẫu key được tạo:")
    if keys:
        print("Key đầu tiên:")
        for i in range(min(5, len(keys))):
            print(f"  {i+1:2d}. {keys[i]}")
        
        print("\nKey cuối cùng:")
        for i in range(max(0, len(keys)-5), len(keys)):
            print(f"  {i+1:2d}. {keys[i]}")
        
        # Phân tích cấu trúc key
        with_customer_code = [k for k in keys if not k.startswith('VNPT_')]
        without_customer_code = [k for k in keys if k.startswith('VNPT_')]
        
        print(f"\n📈 Thống kê:")
        print(f"  - Key có mã KH: {len(with_customer_code)}")
        print(f"  - Key không có mã KH: {len(without_customer_code)}")

if __name__ == "__main__":
    test_rapid_processing()
