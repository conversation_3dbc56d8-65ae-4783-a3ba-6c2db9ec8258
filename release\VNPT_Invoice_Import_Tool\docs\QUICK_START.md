# 🚀 Hướng dẫn nhanh - Tool Import Biên Lai VNPT

**© 2025 Trung tâm CNTT - VNPT Cà Mau**

## 📋 Chuẩn bị

1. **Cài đặt Python 3.8+**
2. **Chạy file `run_tool.bat`** (Windows) hoặc:
   ```bash
   pip install -r requirements.txt
   python invoice_import_tool.py
   ```

## ⚡ Sử dụng nhanh

### Bước 1: Cấu hình VNPT
1. Mở tab **"⚙️ Cấu hình"**
2. Nhập thông tin VNPT:
   - URL VNPT: `https://xxxxxxxxxx.vnpt-invoice.com.vn/`
   - Account, Password, Username, Password
3. Nhấn **"🔍 Test Kết Nối"**
4. Nhấn **"💾 Lưu Cấu Hình"**

### Bước 2: Tạo dữ liệu mẫu
1. Mở tab **"📊 Import Excel"**
2. Nhấn **"📝 Tạo Mẫu"** → Lưu file Excel → **Tự động load preview**
3. Mở file Excel và chỉnh sửa dữ liệu theo nhu cầu

### Bước 3: Import biên lai
1. Nhấn **"📂 Chọn File"** → Chọn file Excel → **Tự động load preview**
2. Kiểm tra dữ liệu trong bảng preview
3. Chọn một trong hai tùy chọn:
   - **"📥 Import Only"** → Chỉ import, không publish
   - **"🚀 Import & Publish"** → Import và publish cùng lúc
4. Theo dõi progress và xem kết quả trong tab **"📋 Log"**

> 💡 **Tip**: File Excel sẽ được tự động load và hiển thị preview ngay sau khi chọn!

> 🔄 **Import vs Publish**:
> - **Import Only**: Biên lai được tạo nhưng chưa được xuất ra
> - **Import & Publish**: Biên lai được tạo và xuất ra ngay lập tức

## 📊 Format Excel cần thiết

| Cột | Mô tả | Ví dụ |
|-----|-------|-------|
| STT | Số thứ tự | 1, 2, 3... |
| Ma_KH | Mã khách hàng | KH001 |
| DonViNop | Đơn vị nộp | Công ty TNHH ABC |
| Dia_Chi | Địa chỉ | 123 Đường ABC, Q1, HCM |
| MST | Mã số thuế | 0123456789 |
| MDVQHNSach | Mã quan hệ ngân sách | MB001 |
| CCCDan | Căn cước công dân | 001234567890 |
| NguoiNopPhi | Người nộp phí | Nguyễn Văn A |
| So_Tien | Số tiền | 1000000 |
| Tong_Tien | Tổng tiền | 1000000 |
| Noi_Dung | Nội dung biên lai | Tiền điện tháng 1/2024 |
| Ngay_Lap | Ngày lập | 2024-01-15 |
| Ghi_Chu | Ghi chú | Khách hàng VIP |

## 🎨 Giao diện Bootstrap

Tool sử dụng **ttkbootstrap** với theme **"cosmo"** tạo giao diện đẹp mắt:
- 🌈 Màu sắc Bootstrap hiện đại
- 📱 Responsive design
- 🎯 Status indicators với emoji
- 📊 Progress bars có animation
- 🔘 Buttons với style đẹp

## 🔧 Tùy chọn nâng cao

- **Pattern/Serial**: Cấu hình mẫu số hóa đơn
- **Convert TCVN3**: Chuyển đổi encoding (0: Không, 1: Có)
- **Auto Publish**: Tự động xuất biên lai sau import

## 📝 Log và Debug

- Tất cả hoạt động được ghi log trong tab **"📋 Log"**
- File log chi tiết: `invoice_import.log`
- Status indicator hiển thị trạng thái kết nối real-time

## ⚠️ Lưu ý

- File config `config.json` chứa mật khẩu → Cần bảo mật
- Backup dữ liệu trước khi import hàng loạt
- Test với ít biên lai trước khi import số lượng lớn
