#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test để kiểm tra UUID key format cho VNPT
"""

import pandas as pd
from datetime import datetime
import re
import uuid

# Tạo class test với UUID key
class TestUUIDKeyFormat:
    def __init__(self):
        pass
    
    def create_invoice_xml(self, row) -> str:
        """Tạo XML data cho biên lai từ dữ liệu row theo format VNPT"""
        xml_template = """<?xml version="1.0" encoding="UTF-8"?>
<Invoices>
    <Inv>
        <key>{key}</key>
        <Invoice>
            <CusCode>{cus_code}</CusCode>
            <Total>{total}</Total>
        </Invoice>
    </Inv>
</Invoices>"""

        # Helper function
        def safe_get(value):
            return str(value).strip() if value and str(value).strip() != 'nan' else ''

        # Extract data từ row
        cus_code = safe_get(row.get('Ma_KH', ''))
        
        # Tạo key unique sử dụng UUID
        # Tạo UUID4 (random UUID) để đảm bảo tính duy nhất tuyệt đối
        unique_id = str(uuid.uuid4()).replace('-', '')  # Loại bỏ dấu gạch ngang
        
        if cus_code and cus_code.strip():
            key = f"{cus_code.strip()}_{unique_id}"
        else:
            # Nếu không có mã khách hàng, chỉ dùng UUID
            key = unique_id

        total = int(row.get('Tong_Tien', 100000))

        return xml_template.format(
            key=key,
            cus_code=cus_code,
            total=total
        )

def test_uuid_key_format():
    """Test UUID key format cho VNPT"""
    
    print("🆔 Test UUID key format cho VNPT")
    print("=" * 50)
    
    # Tạo dữ liệu test với cả 2 trường hợp: có và không có mã KH
    test_data = [
        # Có mã khách hàng
        {'STT': 1, 'Ma_KH': 'KH_01', 'Tong_Tien': 100000},
        {'STT': 2, 'Ma_KH': 'KH002', 'Tong_Tien': 200000},
        {'STT': 3, 'Ma_KH': 'CUST123', 'Tong_Tien': 300000},
        
        # Không có mã khách hàng
        {'STT': 4, 'Ma_KH': '', 'Tong_Tien': 400000},
        {'STT': 5, 'Ma_KH': None, 'Tong_Tien': 500000},
        {'STT': 6, 'Ma_KH': '   ', 'Tong_Tien': 600000},  # Chỉ có khoảng trắng
    ]
    
    # Tạo DataFrame
    df = pd.DataFrame(test_data)
    
    # Tạo instance của tool test
    tool = TestUUIDKeyFormat()
    
    # Tạo XML cho từng hóa đơn và thu thập keys
    keys = []
    print("📋 Tạo UUID key cho từng trường hợp:")
    print("-" * 50)
    
    for index, row in df.iterrows():
        xml_data = tool.create_invoice_xml(row)
        
        # Extract key từ XML
        key_match = re.search(r'<key>(.*?)</key>', xml_data)
        if key_match:
            key = key_match.group(1)
            keys.append(key)
            
            ma_kh = row.get('Ma_KH', '')
            ma_kh_display = f"'{ma_kh}'" if ma_kh and str(ma_kh).strip() else "RỖNG"
            print(f"Hóa đơn {index+1}: Mã KH = {ma_kh_display:10} → Key = {key}")
        else:
            print(f"Hóa đơn {index+1}: ❌ Không tìm thấy key")
    
    print("\n" + "=" * 50)
    print("📊 Phân tích kết quả:")
    
    # Phân loại key
    with_customer_code = []
    without_customer_code = []
    
    for i, key in enumerate(keys):
        if '_' in key:
            with_customer_code.append((i+1, key))
        else:
            without_customer_code.append((i+1, key))
    
    print(f"\n✅ Key có mã khách hàng ({len(with_customer_code)}):")
    for invoice_num, key in with_customer_code:
        # Hiển thị key rút gọn để dễ đọc
        customer_part = key.split('_')[0]
        uuid_part = key.split('_')[1]
        print(f"  Hóa đơn {invoice_num}: {customer_part}_{uuid_part[:8]}...{uuid_part[-8:]}")
    
    print(f"\n🔢 Key không có mã khách hàng ({len(without_customer_code)}):")
    for invoice_num, key in without_customer_code:
        # Hiển thị UUID rút gọn để dễ đọc
        print(f"  Hóa đơn {invoice_num}: {key[:8]}...{key[-8:]}")
    
    # Kiểm tra tính duy nhất
    print(f"\n🔍 Kiểm tra tính duy nhất:")
    print(f"  - Tổng số key: {len(keys)}")
    print(f"  - Số key duy nhất: {len(set(keys))}")
    
    if len(keys) == len(set(keys)):
        print("  ✅ Tất cả key đều duy nhất!")
    else:
        print("  ❌ Có key bị trùng lặp!")
    
    # Kiểm tra format UUID
    print(f"\n📏 Kiểm tra format UUID:")
    uuid_pattern = re.compile(r'^[a-f0-9]{32}$')
    
    for i, key in enumerate(keys):
        if '_' in key:
            # Key có mã khách hàng
            parts = key.split('_')
            if len(parts) == 2:
                customer_code, uuid_part = parts
                if uuid_pattern.match(uuid_part):
                    print(f"  ✅ Hóa đơn {i+1}: Format hợp lệ ({customer_code}_UUID)")
                else:
                    print(f"  ❌ Hóa đơn {i+1}: UUID không hợp lệ")
            else:
                print(f"  ❌ Hóa đơn {i+1}: Format key không đúng")
        else:
            # Key chỉ là UUID
            if uuid_pattern.match(key):
                print(f"  ✅ Hóa đơn {i+1}: UUID hợp lệ")
            else:
                print(f"  ❌ Hóa đơn {i+1}: UUID không hợp lệ")
    
    print(f"\n📝 Ưu điểm của UUID key:")
    print(f"  ✅ Đảm bảo tính duy nhất tuyệt đối")
    print(f"  ✅ Không phụ thuộc vào thời gian")
    print(f"  ✅ Tương thích tốt với hệ thống database")
    print(f"  ✅ Độ dài cố định (32 ký tự hex)")
    print(f"  ✅ Không có prefix có thể gây xung đột")

def test_uuid_collision():
    """Test khả năng trùng lặp của UUID (rất hiếm)"""
    print(f"\n🎲 Test khả năng trùng lặp UUID (tạo 10,000 UUID):")
    
    uuids = set()
    for i in range(10000):
        new_uuid = str(uuid.uuid4()).replace('-', '')
        uuids.add(new_uuid)
    
    print(f"  - Tạo: 10,000 UUID")
    print(f"  - Duy nhất: {len(uuids)}")
    
    if len(uuids) == 10000:
        print(f"  ✅ Không có UUID nào trùng lặp!")
    else:
        print(f"  ⚠️  Có {10000 - len(uuids)} UUID trùng lặp (rất hiếm!)")

if __name__ == "__main__":
    test_uuid_key_format()
    test_uuid_collision()
