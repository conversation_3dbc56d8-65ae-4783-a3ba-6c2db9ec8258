@echo off
echo.
echo ========================================
echo    Build with New Icon - VNPT Tool
echo    Force rebuild voi icon moi
echo    (c) 2025 Trung tam CNTT - VNPT Ca Mau  
echo ========================================
echo.

REM Kiem tra icon
echo [INFO] Kiem tra icon moi...
if exist "icon.ico" (
    echo [OK] Tim thay icon.ico
    for %%I in ("icon.ico") do echo [INFO] Kich thuoc icon: %%~zI bytes
    echo [INFO] Thoi gian cap nhat icon:
    forfiles /m icon.ico /c "cmd /c echo @fdate @ftime"
) else (
    echo [ERROR] Khong tim thay icon.ico!
    pause
    exit /b 1
)

echo.
echo [INFO] Xoa cache PyInstaller de force rebuild...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM Xoa cache PyInstaller trong AppData
echo [INFO] Xoa cache PyInstaller trong AppData...
if exist "%LOCALAPPDATA%\pyinstaller" rmdir /s /q "%LOCALAPPDATA%\pyinstaller"

echo [OK] Da xoa tat ca cache

echo.
echo [INFO] Build voi icon moi...
echo [INFO] Su dung duong dan tuyet doi cho icon
echo [INFO] Current directory: %CD%
echo [INFO] Icon path: %CD%\icon.ico

REM Build với force rebuild
pyinstaller --clean --noconfirm --log-level=INFO build_exe.spec

if errorlevel 1 (
    echo.
    echo [ERROR] Build that bai!
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build thanh cong!

if exist "dist\VNPT_Invoice_Import_Tool.exe" (
    echo [INFO] Thong tin file EXE:
    for %%I in ("dist\VNPT_Invoice_Import_Tool.exe") do (
        echo        Kich thuoc: %%~zI bytes
        set /a size_mb=%%~zI/1024/1024
        echo        Tuong duong: !size_mb! MB
    )
    
    echo.
    echo [INFO] Kiem tra icon trong EXE...
    echo [INFO] File EXE da duoc tao voi icon moi
    
    echo.
    echo [QUESTION] Ban co muon test file EXE khong? (Y/N)
    set /p choice=
    if /i "!choice!"=="Y" (
        echo [INFO] Dang khoi chay...
        start "" "dist\VNPT_Invoice_Import_Tool.exe"
    )
) else (
    echo [ERROR] Khong tim thay file EXE!
)

echo.
echo [INFO] Build hoan thanh!
pause
