@echo off
echo.
echo ========================================
echo    Package Release - VNPT Invoice Tool
echo    Dong goi phan phoi ung dung
echo    (c) 2025 Trung tam CNTT - VNPT Ca Mau  
echo ========================================
echo.

REM Kiem tra file EXE
if not exist "dist\VNPT_Invoice_Import_Tool.exe" (
    echo [ERROR] Khong tim thay file EXE!
    echo [INFO] Vui long chay build_exe.bat truoc
    pause
    exit /b 1
)

echo [INFO] Tao thu muc release...
if exist "release" rmdir /s /q "release"
mkdir "release"
mkdir "release\VNPT_Invoice_Import_Tool"
mkdir "release\VNPT_Invoice_Import_Tool\docs"
mkdir "release\VNPT_Invoice_Import_Tool\samples"

echo [INFO] Copy file EXE...
copy "dist\VNPT_Invoice_Import_Tool.exe" "release\VNPT_Invoice_Import_Tool\"

echo [INFO] Copy tai lieu...
copy "README.md" "release\VNPT_Invoice_Import_Tool\docs\"
copy "QUICK_START.md" "release\VNPT_Invoice_Import_Tool\docs\"
copy "LICENSE" "release\VNPT_Invoice_Import_Tool\docs\"
copy "vnpt_receipt_integration_docs.md" "release\VNPT_Invoice_Import_Tool\docs\"

echo [INFO] Copy file mau...
copy "config_sample.json" "release\VNPT_Invoice_Import_Tool\samples\"

echo [INFO] Tao file huong dan su dung...
echo # VNPT Invoice Import Tool - Release Package > "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo. >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo Ban quyen thuoc ve Trung tam CNTT - VNPT Ca Mau >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo (c) 2025 VNPT Ca Mau IT Center. All rights reserved. >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo. >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo CACH SU DUNG: >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo 1. Double-click file VNPT_Invoice_Import_Tool.exe de chay >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo 2. Khong can cai dat Python hay thu vien bo sung >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo 3. Cau hinh thong tin VNPT trong tab "Cau hinh" >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo 4. Tao file Excel mau hoac chon file Excel co san >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo 5. Import bien lai bang cac nut "Import Only" hoac "Import & Publish" >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo. >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo TAI LIEU CHI TIET: >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo - docs\README.md: Huong dan day du >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo - docs\QUICK_START.md: Huong dan nhanh >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo - docs\LICENSE: Thong tin ban quyen >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo - samples\config_sample.json: File cau hinh mau >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo. >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo HO TRO KY THUAT: >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo Email: <EMAIL> >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"
echo Website: https://vnpt-camau.vn >> "release\VNPT_Invoice_Import_Tool\HUONG_DAN_SU_DUNG.txt"

echo [INFO] Tao file version info...
echo VNPT Invoice Import Tool > "release\VNPT_Invoice_Import_Tool\VERSION.txt"
echo Version: 2.0.0 Bootstrap Edition >> "release\VNPT_Invoice_Import_Tool\VERSION.txt"
echo Build Date: %date% %time% >> "release\VNPT_Invoice_Import_Tool\VERSION.txt"
echo Copyright: (c) 2025 VNPT Ca Mau IT Center >> "release\VNPT_Invoice_Import_Tool\VERSION.txt"

echo [INFO] Kiem tra kich thuoc file...
for %%I in ("release\VNPT_Invoice_Import_Tool\VNPT_Invoice_Import_Tool.exe") do echo File EXE: %%~zI bytes

echo.
echo [SUCCESS] Package hoan thanh!
echo [INFO] Thu muc release: release\VNPT_Invoice_Import_Tool\
echo [INFO] Noi dung package:
dir /b "release\VNPT_Invoice_Import_Tool\"
echo.
echo [INFO] Noi dung docs:
dir /b "release\VNPT_Invoice_Import_Tool\docs\"
echo.
echo [INFO] Noi dung samples:
dir /b "release\VNPT_Invoice_Import_Tool\samples\"

echo.
echo [QUESTION] Ban co muon tao file ZIP khong? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo [INFO] Tao file ZIP...
    powershell -command "Compress-Archive -Path 'release\VNPT_Invoice_Import_Tool' -DestinationPath 'VNPT_Invoice_Import_Tool_v2.0.zip' -Force"
    if exist "VNPT_Invoice_Import_Tool_v2.0.zip" (
        echo [SUCCESS] Da tao file ZIP: VNPT_Invoice_Import_Tool_v2.0.zip
        for %%I in ("VNPT_Invoice_Import_Tool_v2.0.zip") do echo Kich thuoc ZIP: %%~zI bytes
    ) else (
        echo [ERROR] Khong the tao file ZIP!
    )
)

echo.
echo [INFO] Hoan thanh dong goi release!
pause
